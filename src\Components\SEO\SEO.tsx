
import type { JSX } from "react";
import { Helmet } from "react-helmet";


interface SEOProps {
    title?: string;
    description?: string;
    keywords?: string;
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
    ogUrl?: string;
}

export function SEO({
    title = "Premium Real Estate in Israel | Luxury Properties for Sale & Rent",
    description = "Discover luxury apartments, villas, and premium properties for sale and rent in Tel Aviv, Jerusalem, Haifa, and across Israel. Expert bilingual real estate services.",
    keywords = "real estate Israel, luxury apartments Tel Aviv, properties for sale Jerusalem, rental properties Haifa, Israel property investment, bilingual real estate agent",
    ogTitle,
    ogDescription,
    ogImage = "/real-estate-hero.jpg",
    ogUrl = "https://yourdomain.com",
}: SEOProps): JSX.Element {
    const metaTitle = title || "Premium Real Estate Israel";
    const metaDescription = description;
    const metaKeywords = keywords;
    const metaOgTitle = ogTitle || metaTitle;
    const metaOgDescription = ogDescription || metaDescription;

    return (
        <Helmet>
            {/* Basic Meta Tags */}
            <title>{metaTitle}</title>
            <meta name="description" content={metaDescription} />
            <meta name="keywords" content={metaKeywords} />

            {/* Open Graph / Facebook */}
            <meta property="og:type" content="website" />
            <meta property="og:url" content={ogUrl} />
            <meta property="og:title" content={metaOgTitle} />
            <meta property="og:description" content={metaOgDescription} />
            <meta property="og:image" content={ogImage} />

            {/* Twitter */}
            <meta property="twitter:card" content="summary_large_image" />
            <meta property="twitter:url" content={ogUrl} />
            <meta property="twitter:title" content={metaOgTitle} />
            <meta property="twitter:description" content={metaOgDescription} />
            <meta property="twitter:image" content={ogImage} />

            {/* Canonical URL */}
            <link rel="canonical" href={ogUrl} />
        </Helmet>
    );
}
