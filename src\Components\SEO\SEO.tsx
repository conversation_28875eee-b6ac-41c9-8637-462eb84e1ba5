
import type { JSX } from "react";
import { Helmet } from "react-helmet";


interface SEOProps {
    title?: string;
    description?: string;
    keywords?: string;
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
    ogUrl?: string;
}

export function SEO({
    title = "SlidingJob - Showcase Your Projects",
    description = "SlidingJob - Where recruiters see projects first and then the person behind it if there's a match. Showcase your skills regardless of work experience.",
    keywords = "job search, projects, recruiters, developers, portfolio, skills, hiring",
    ogTitle,
    ogDescription,
    ogImage = "/src/assets/sliding-job-logo.webp",
    ogUrl = "https://slidingjob.com",
}: SEOProps): JSX.Element {
    const metaTitle = title || "SlidingJob";
    const metaDescription = description;
    const metaKeywords = keywords;
    const metaOgTitle = ogTitle || metaTitle;
    const metaOgDescription = ogDescription || metaDescription;

    return (
        <Helmet>
            {/* Basic Meta Tags */}
            <title>{metaTitle}</title>
            <meta name="description" content={metaDescription} />
            <meta name="keywords" content={metaKeywords} />

            {/* Open Graph / Facebook */}
            <meta property="og:type" content="website" />
            <meta property="og:url" content={ogUrl} />
            <meta property="og:title" content={metaOgTitle} />
            <meta property="og:description" content={metaOgDescription} />
            <meta property="og:image" content={ogImage} />

            {/* Twitter */}
            <meta property="twitter:card" content="summary_large_image" />
            <meta property="twitter:url" content={ogUrl} />
            <meta property="twitter:title" content={metaOgTitle} />
            <meta property="twitter:description" content={metaOgDescription} />
            <meta property="twitter:image" content={ogImage} />

            {/* Canonical URL */}
            <link rel="canonical" href={ogUrl} />
        </Helmet>
    );
}
