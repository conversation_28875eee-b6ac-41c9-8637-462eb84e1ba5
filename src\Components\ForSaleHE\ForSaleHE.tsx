import type { JSX } from "react";
import { useState } from "react";
import { motion } from "framer-motion";
import { SEO } from "../SEO/SEO";
import { PhoneInput } from "../PhoneInput/PhoneInput";
import { LeadUser } from "../../models/LeadUser";
import leadService from "../../services/LeadService";
import "./ForSaleHE.css";

export function ForSaleHE(): JSX.Element {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        message: ""
    });
    const [privacyAccepted, setPrivacyAccepted] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!privacyAccepted) {
            setSubmitMessage("אנא אשר את מדיניות הפרטיות כדי להמשיך.");
            return;
        }

        setIsSubmitting(true);
        setSubmitMessage("");

        try {
            const leadUser = new LeadUser(
                0, // id will be set by backend
                formData.firstName,
                formData.lastName,
                formData.email,
                formData.phoneNumber,
                formData.message,
                "for sale hebrew"
            );

            await leadService.addLead(leadUser);
            setSubmitMessage("תודה! ניצור איתך קשר בקרוב.");
            setFormData({
                firstName: "",
                lastName: "",
                email: "",
                phoneNumber: "",
                message: ""
            });
            setPrivacyAccepted(false);
        } catch (error) {
            setSubmitMessage("משהו השתבש. אנא נסה שוב.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <SEO
                title="נכסי יוקרה למכירה בישראל | השקעות נדלן פרימיום תל אביב, ירושלים, חיפה | מתווך דו לשוני"
                description="גלה דירות בלעדיות, פנטהאוזים, וילות ונכסי יוקרה למכירה בישראל. שירותי השקעות נדלן דו-לשוניים מקצועיים בתל אביב, ירושלים, חיפה, הרצליה, נתניה. נכסים מחוץ לשוק, פרויקטים חדשים, דירות יד שנייה. ייעוץ השקעות מקצועי ותמיכה משפטית."
                keywords="קניית נכסים ישראל, דירות יוקרה תל אביב, בתים למכירה ירושלים, נדלן חיפה, השקעות נדלן ישראל, מתווך דו לשוני, פנטהאוז למכירה תל אביב, וילה למכירה ירושלים, נדלן יוקרה ישראל, השקעות נכסים ישראל, נכסים מחוץ לשוק ישראל, פרויקטים חדשים תל אביב, דירות יד שנייה ירושלים, השקעות נדלן ישראל, נכסים בינלאומיים ישראל, מתווך דובר אנגלית ישראל, נדלן עברית אנגלית, רכישת נכס ישראל, נכסי השקעה תל אביב, בתי יוקרה ירושלים, נכסים בלעדיים חיפה, מתווך נדלן ישראל, חיפוש נכס תל אביב, בית למכירה ירושלים, דירה למכירה נתניה, נדלן פרימיום ישראל, נכסים יוקרתיים ישראל, בתי בכירים ישראל, נכסי חוף ישראל, דירות מרכז העיר תל אביב, דירות יוקרה ירושלים, הזדמנויות השקעה ישראל, תיק נכסים ישראל, שוק הנדלן ישראל, הערכת נכסים ישראל, תמיכה משפטית רכישת נכס"
                ogTitle="נכסי יוקרה למכירה בישראל - שירותי השקעות נדלן פרימיום"
                ogDescription="מצא את השקעת הנכס המושלמת שלך בישראל עם הצוות הדו-לשוני המקצועי שלנו. דירות יוקרה, פנטהאוזים, וילות ונכסים בלעדיים מחוץ לשוק ברחבי תל אביב, ירושלים, חיפה ועוד. ייעוץ השקעות מקצועי ותמיכה משפטית."
                ogImage="/sale-hero-he.jpg"
                ogUrl="https://yourdomain.com/for-sale-he"
            />
            <div className="ForSaleHE" dir="rtl">
                {/* Hero Section */}
                <motion.section
                    className="hero-section"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <div className="hero-content">
                        <h1>גלה נכסי יוקרה למכירה בישראל</h1>
                        <p className="hero-subtitle">
                            דירות בלעדיות, וילות ונכסי פרימיום ברחבי תל אביב, ירושלים, חיפה ועוד.
                            שירות דו-לשוני מקצועי עם גישה להזדמנויות הנדלן הטובות ביותר בישראל.
                        </p>
                        <motion.button
                            className="cta-button primary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            מצא את נכס החלומות שלך
                        </motion.button>
                    </div>
                    <div className="hero-image">
                        <img src="/api/placeholder/600/400" alt="נכס יוקרה למכירה בישראל" />
                    </div>
                </motion.section>

                {/* Services Section */}
                <motion.section
                    className="services-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>שירותי מכירת נכסים פרימיום</h2>
                        <div className="services-grid">
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">🏢</div>
                                <h3>דירות יוקרה</h3>
                                <p>דירות מובחרות בשכונות היוקרה של תל אביב עם נוף מרהיב לעיר ולים.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">🏰</div>
                                <h3>וילות בלעדיות</h3>
                                <p>וילות מפוארות ובתים במיקומים מובחרים ברחבי ישראל, מושלמים למגורים יוקרתיים והשקעה.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">💎</div>
                                <h3>הזדמנויות השקעה</h3>
                                <p>נכסים בלעדיים מחוץ לשוק והזדמנויות השקעה עם פוטנציאל תשואה גבוה.</p>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Why Choose Us Section */}
                <motion.section
                    className="why-choose-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>למה לבחור בשירותי מכירת הנכסים שלנו?</h2>
                        <div className="features-grid">
                            <div className="feature-item">
                                <h3>🌍 מומחיות דו-לשונית</h3>
                                <p>הצוות שלנו מספק תקשורת חלקה בעברית ואנגלית, מה שמבטיח שתבין כל פרט ברכישת הנכס שלך.</p>
                            </div>
                            <div className="feature-item">
                                <h3>🏆 מיקומים פרימיום</h3>
                                <p>גישה בלעדית לנכסים הנחשקים ביותר בתל אביב, ירושלים, הרצליה, נתניה ומיקומים מובחרים נוספים בישראל.</p>
                            </div>
                            <div className="feature-item">
                                <h3>💼 ייעוץ השקעות</h3>
                                <p>ייעוץ מקצועי בהזדמנויות השקעה בנכסים, מגמות שוק ותשואות פוטנציאליות בשוק הנדלן הישראלי.</p>
                            </div>
                            <div className="feature-item">
                                <h3>🔒 עסקאות מאובטחות</h3>
                                <p>תמיכה משפטית מלאה וטיפול בעסקאות מאובטחות, מה שמבטיח שרכישת הנכס שלך תהיה חלקה ומוגנת.</p>
                            </div>
                        </div>
                    </div>
                </motion.section>

                {/* Testimonials Section */}
                <motion.section
                    className="testimonials-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>סיפורי הצלחה</h2>
                        <div className="testimonials-grid">
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"רכשנו פנטהאוס מדהים בתל אביב בעזרתם. השירות הדו-לשוני והמומחיות בשוק עשו את כל ההבדל!"</p>
                                    <div className="testimonial-author">
                                        <strong>רוברט וליסה כ.</strong>
                                        <span>משקיעים בינלאומיים</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"מצאנו את וילת החלומות שלנו בהרצליה. הידע שלהם בשוק היוקרה וכישורי המשא ומתן חסכו לנו הרבה כסף."</p>
                                    <div className="testimonial-author">
                                        <strong>יונתן ואמה ש.</strong>
                                        <span>קוני בתי יוקרה</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"ייעוץ השקעות מעולה! תיק הנכסים שלנו בישראל עלה על כל הציפיות הודות למומחיות שלהם."</p>
                                    <div className="testimonial-author">
                                        <strong>אלכסנדר מ.</strong>
                                        <span>משקיע נכסים</span>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Contact Form Section */}
                <motion.section
                    id="contact-form"
                    className="contact-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>מצא את נכס החלומות שלך היום</h2>
                        <p className="section-subtitle">ספר לנו על הנכס האידיאלי שלך והתקציב שלך, ואנחנו נמצא עבורך את ההתאמה המושלמת.</p>

                        <form onSubmit={handleSubmit} className="contact-form">
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">שם פרטי *</label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        value={formData.firstName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">שם משפחה *</label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        value={formData.lastName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="email">אימייל *</label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <PhoneInput
                                    value={formData.phoneNumber}
                                    onChange={(value) => setFormData(prev => ({ ...prev, phoneNumber: value }))}
                                    label="מספר טלפון"
                                    placeholder="50-123-4567"
                                    required
                                    id="phoneNumber"
                                    name="phoneNumber"
                                    isRTL={true}
                                />
                            </div>
                            <div className="form-group">
                                <label htmlFor="message">ספר לנו על הנכס האידיאלי שלך</label>
                                <textarea
                                    id="message"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    rows={4}
                                    placeholder="העדפות מיקום, סוג נכס, טווח תקציב, לוח זמנים וכו'"
                                />
                            </div>

                            <div className="privacy-checkbox-group">
                                <label className="privacy-checkbox-label">
                                    <input
                                        type="checkbox"
                                        checked={privacyAccepted}
                                        onChange={(e) => setPrivacyAccepted(e.target.checked)}
                                        required
                                        className="privacy-checkbox"
                                    />
                                    <span className="checkmark"></span>
                                    <span className="privacy-text">
                                        אני מסכים ל<a href="/privacy-policy" target="_blank" rel="noopener noreferrer">מדיניות הפרטיות</a> ומסכים
                                        להיות מוזמן ליצירת קשר באמצעות טלפון, אימייל או הודעת טקסט בנוגע להזדמנויות נדלן.
                                    </span>
                                </label>
                            </div>
                            <div className="form-button-container">
                                <motion.button
                                    type="submit"
                                    className="cta-button primary"
                                    disabled={isSubmitting}
                                    whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
                                    whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                                >
                                    {isSubmitting ? "שולח..." : "מצא לי את נכס החלומות"}
                                </motion.button>
                                {submitMessage && (
                                    <div className={`submit-message ${submitMessage.includes('תודה') ? 'success' : 'error'}`}>
                                        {submitMessage}
                                    </div>
                                )}
                            </div>
                        </form>
                    </div>
                </motion.section>

                {/* Final CTA Section */}
                <motion.section
                    className="final-cta-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>מוכן להשקיע בנדלן ישראלי?</h2>
                        <p>הצטרף למשקיעים מצליחים שבחרו בישראל להשקעות הנכסים שלהם. ייעוץ מקצועי בכל שלב בדרך.</p>
                        <motion.button
                            className="cta-button secondary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            התחל את מסע ההשקעה שלך
                        </motion.button>
                    </div>
                </motion.section>
            </div>
        </>
    );
}
