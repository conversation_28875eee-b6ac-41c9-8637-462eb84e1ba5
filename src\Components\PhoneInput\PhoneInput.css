/* PhoneInput Component Styles */
.phone-input-container {
    display: flex;
    flex-direction: column;
    position: relative;
}

.phone-input-label {
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.phone-input-container.rtl .phone-input-label {
    text-align: right;
}

.phone-input-wrapper {
    display: flex;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.phone-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.country-selector {
    position: relative;
    flex-shrink: 0;
}

.country-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    padding: 16px 14px;
    border: none;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    min-width: 140px;
    border-right: 1px solid #e9ecef;
}

.phone-input-container.rtl .country-button {
    border-right: none;
    border-left: 1px solid #e9ecef;
}

.country-button:hover {
    background: #e9ecef;
}

.country-flag {
    font-size: 1.3rem;
    flex-shrink: 0;
}

.country-code {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
    flex: 1;
    text-align: left;
    margin-left: 0.5rem;
}

.dropdown-arrow {
    font-size: 0.8rem;
    color: #6c757d;
    flex-shrink: 0;
}

.phone-input-container.rtl .country-code {
    text-align: right;
    margin-left: 0;
    margin-right: 0.5rem;
}

.country-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 280px;
    overflow: hidden;
    margin-top: 4px;
    min-width: 300px;
}

.phone-input-container.rtl .country-dropdown {
    left: auto;
    right: 0;
}



.countries-list {
    max-height: 280px;
    overflow-y: auto;
}

.country-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    width: 100%;
    padding: 14px 16px;
    border: none;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.phone-input-container.rtl .country-option {
    text-align: right;
}

.country-option:hover {
    background: #f8f9fa;
}

.country-option.selected {
    background: #667eea;
    color: white;
}

.country-option.selected .country-dial-code {
    color: white;
}

.country-option .country-flag {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.country-name {
    flex: 1;
    font-size: 0.95rem;
    font-weight: 500;
    text-align: left;
    margin-left: 0.75rem;
}

.country-dial-code {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
    flex-shrink: 0;
    min-width: 60px;
    text-align: right;
}

.phone-number-input {
    flex: 1;
    padding: 16px 20px;
    border: none;
    outline: none;
    font-size: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.phone-input-container.rtl .phone-number-input {
    text-align: right;
    direction: rtl;
}

.phone-number-input::placeholder {
    color: #9ca3af;
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .phone-input-wrapper {
        flex-direction: column;
        border-radius: 12px;
    }

    .country-button {
        min-width: 100%;
        padding: 14px 16px;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        border-radius: 12px 12px 0 0;
    }

    .phone-input-container.rtl .country-button {
        border-left: none;
        border-bottom: 1px solid #e9ecef;
    }

    .country-code {
        font-size: 0.9rem;
    }

    .country-dropdown {
        max-height: 250px;
        left: 0;
        right: 0;
    }

    .phone-number-input {
        padding: 14px 16px;
        font-size: 0.9rem;
        border-radius: 0 0 12px 12px;
    }

    .phone-input-label {
        font-size: 0.9rem;
    }
}

/* Tablet Design */
@media (max-width: 1024px) and (min-width: 769px) {
    .country-button {
        min-width: 110px;
        padding: 15px 12px;
    }

    .phone-number-input {
        padding: 15px 18px;
        font-size: 1rem;
    }

    .country-dropdown {
        max-height: 280px;
    }
}

@media (max-width: 480px) {
    .phone-input-wrapper {
        flex-direction: column;
    }

    .country-button {
        min-width: 100%;
        padding: 12px 14px;
        justify-content: center;
        border-radius: 12px 12px 0 0;
    }

    .country-flag {
        font-size: 1rem;
    }

    .country-code {
        font-size: 0.8rem;
    }

    .phone-number-input {
        padding: 12px 14px;
        font-size: 0.9rem;
        border-radius: 0 0 12px 12px;
    }

    .country-dropdown {
        max-height: 200px;
        left: 0;
        right: 0;
    }

    .phone-input-label {
        font-size: 0.85rem;
    }

    .dropdown-search {
        padding: 10px;
    }

    .search-input {
        padding: 6px 10px;
        font-size: 0.85rem;
    }

    .country-option {
        padding: 10px;
    }

    .country-name {
        font-size: 0.85rem;
    }

    .country-dial-code {
        font-size: 0.75rem;
    }
}

/* Accessibility */
.country-button:focus,
.phone-number-input:focus,
.search-input:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Animation for dropdown */
.countries-list {
    scrollbar-width: thin;
    scrollbar-color: #667eea #f1f1f1;
}

.countries-list::-webkit-scrollbar {
    width: 6px;
}

.countries-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.countries-list::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.countries-list::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}
