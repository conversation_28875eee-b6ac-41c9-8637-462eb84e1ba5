import type { JSX } from "react";
import { motion } from "framer-motion";
import "./Section.css";

interface SectionProps {
    children: React.ReactNode;
    className?: string;
    background?: 'light' | 'dark' | 'gradient' | 'transparent';
    padding?: 'small' | 'medium' | 'large';
    animate?: boolean;
    id?: string;
}

export function Section({
    children,
    className = '',
    background = 'transparent',
    padding = 'medium',
    animate = true,
    id
}: SectionProps): JSX.Element {
    const sectionClass = `section section-${background} section-${padding} ${className}`;

    if (animate) {
        return (
            <motion.section
                id={id}
                className={sectionClass}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
            >
                <div className="container">
                    {children}
                </div>
            </motion.section>
        );
    }

    return (
        <section id={id} className={sectionClass}>
            <div className="container">
                {children}
            </div>
        </section>
    );
}
