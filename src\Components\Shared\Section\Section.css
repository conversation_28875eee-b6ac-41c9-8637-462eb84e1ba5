/* Shared Section Component Styles */
.section {
    width: 100%;
    position: relative;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Backgrounds */
.section-light {
    background: #ffffff;
    color: #333333;
}

.section-dark {
    background: #1a1a1a;
    color: #ffffff;
}

.section-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

.section-transparent {
    background: transparent;
}

/* Section Padding */
.section-small {
    padding: 40px 0;
}

.section-medium {
    padding: 80px 0;
}

.section-large {
    padding: 120px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .section-small {
        padding: 30px 0;
    }
    
    .section-medium {
        padding: 60px 0;
    }
    
    .section-large {
        padding: 80px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .section-small {
        padding: 20px 0;
    }
    
    .section-medium {
        padding: 40px 0;
    }
    
    .section-large {
        padding: 60px 0;
    }
}
