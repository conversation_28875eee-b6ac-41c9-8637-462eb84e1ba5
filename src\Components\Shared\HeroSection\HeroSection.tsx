import type { JSX } from "react";
import { motion } from "framer-motion";
import { ImageSlideshow } from "../../ImageSlideshow/ImageSlideshow";
import { Button } from "../Button/Button";
import "./HeroSection.css";

interface HeroSectionProps {
    title: string;
    subtitle: string;
    buttonText: string;
    onButtonClick: () => void;
    imageAlt: string;
    autoPlayInterval?: number;
    background?: 'light' | 'dark' | 'gradient';
    className?: string;
}

export function HeroSection({
    title,
    subtitle,
    buttonText,
    onButtonClick,
    imageAlt,
    autoPlayInterval = 4000,
    background = 'gradient',
    className = ''
}: HeroSectionProps): JSX.Element {
    return (
        <motion.section
            className={`hero-section hero-${background} ${className}`}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
        >
            <div className="container">
            <div className="hero-container">
                <div className="hero-content">
                    <motion.h1
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                    >
                        {title}
                    </motion.h1>
                    <motion.p
                        className="hero-subtitle"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.4 }}
                    >
                        {subtitle}
                    </motion.p>
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.6 }}
                    >
                        <Button
                            variant="primary"
                            size="large"
                            onClick={onButtonClick}
                        >
                            {buttonText}
                        </Button>
                    </motion.div>
                </div>
                <motion.div
                    className="hero-image"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                >
                    <ImageSlideshow
                        alt={imageAlt}
                        autoPlayInterval={autoPlayInterval}
                        showArrows={false}
                    />
                </motion.div>
            </div>
            </div>
        </motion.section>
    );
}
