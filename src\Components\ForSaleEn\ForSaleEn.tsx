import type { JSX } from "react";
import { useState } from "react";
import { motion } from "framer-motion";
import { SEO } from "../SEO/SEO";
import { ImageSlideshow } from "../ImageSlideshow/ImageSlideshow";

import { LeadUser } from "../../models/LeadUser";
import leadService from "../../services/LeadService";
import "./ForSaleEn.css";

export function ForSaleEn(): JSX.Element {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        message: ""
    });
    const [privacyAccepted, setPrivacyAccepted] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!privacyAccepted) {
            setSubmitMessage("Please accept the privacy policy to continue.");
            return;
        }

        setIsSubmitting(true);
        setSubmitMessage("");

        try {
            const leadUser = new LeadUser(
                0, // id will be set by backend
                formData.firstName,
                formData.lastName,
                formData.email,
                formData.phoneNumber,
                formData.message,
                "for sale english"
            );

            await leadService.addLead(leadUser);
            setSubmitMessage("Thank you! We'll contact you soon.");
            setFormData({
                firstName: "",
                lastName: "",
                email: "",
                phoneNumber: "",
                message: ""
            });
            setPrivacyAccepted(false);
        } catch (error) {
            setSubmitMessage("Something went wrong. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <SEO
                title="Luxury Properties for Sale in Israel | Premium Real Estate Investment | Tel Aviv, Jerusalem, Haifa"
                description="Discover exclusive apartments, penthouses, villas, and luxury properties for sale in Israel. Expert bilingual real estate investment services in Tel Aviv, Jerusalem, Haifa, Herzliya, Netanya. Off-market properties, new developments, resale apartments. Professional investment guidance and legal support."
                keywords="buy property Israel, luxury apartments Tel Aviv, houses for sale Jerusalem, real estate Haifa, Israel property investment, bilingual real estate agent, penthouse for sale Tel Aviv, villa for sale Jerusalem, luxury real estate Israel, property investment Israel, off market properties Israel, new developments Tel Aviv, resale apartments Jerusalem, real estate investment Israel, international property Israel, English speaking realtor Israel, Hebrew English real estate, property purchase Israel, investment properties Tel Aviv, luxury homes Jerusalem, exclusive properties Haifa, real estate agent Israel, property finder Tel Aviv, house for sale Jerusalem, apartment for sale Netanya, premium real estate Israel, high end properties Israel, executive homes Israel, waterfront properties Israel, city center apartments Tel Aviv, luxury condos Jerusalem, investment opportunities Israel, property portfolio Israel, real estate market Israel, property valuation Israel, legal support property purchase"
                ogTitle="Luxury Properties for Sale in Israel - Premium Real Estate Investment Services"
                ogDescription="Find your dream property investment in Israel with our expert bilingual team. Luxury apartments, penthouses, villas, and exclusive off-market properties across Tel Aviv, Jerusalem, Haifa, and more. Professional investment guidance and legal support."
                ogImage="/sale-hero.jpg"
                ogUrl="https://yourdomain.com/for-sale-en"
            />
            <div className="ForSaleEn">
                {/* Hero Section */}
                <motion.section
                    className="hero-section"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <div className="hero-content">
                        <h1>Discover Luxury Properties for Sale in Israel</h1>
                        <p className="hero-subtitle">
                            Exclusive apartments, villas, and premium properties across Tel Aviv, Jerusalem, Haifa, and beyond.
                            Expert bilingual service with access to the finest real estate opportunities in Israel.
                        </p>
                        <motion.button
                            className="cta-button primary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            Find Your Dream Property
                        </motion.button>
                    </div>
                    <div className="hero-image">
                        <ImageSlideshow
                            alt="Luxury property for sale in Israel"
                            autoPlayInterval={4000}
                            showDots={true}
                            showArrows={false}
                        />
                    </div>
                </motion.section>

                {/* Services Section */}
                <motion.section
                    className="services-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.h2
                            initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            Premium Property Sales Services
                        </motion.h2>
                        <div className="services-grid">
                            <motion.div
                                className="service-card"
                                initial={{ opacity: 0, y: 80 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -15, scale: 1.03 }}
                            >
                                <div className="service-icon">🏢</div>
                                <h3>Luxury Apartments</h3>
                                <p>Premium apartments in Tel Aviv's most prestigious neighborhoods with stunning city and sea views.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                initial={{ opacity: 0, y: 80 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -15, scale: 1.03 }}
                            >
                                <div className="service-icon">🏰</div>
                                <h3>Exclusive Villas</h3>
                                <p>Magnificent villas and houses in prime locations across Israel, perfect for luxury living and investment.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                initial={{ opacity: 0, y: 80 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -15, scale: 1.03 }}
                            >
                                <div className="service-icon">💎</div>
                                <h3>Investment Opportunities</h3>
                                <p>Exclusive off-market properties and investment opportunities with high potential returns.</p>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Why Choose Us Section */}
                <motion.section
                    className="why-choose-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.h2
                            initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            Why Choose Our Property Sales Services?
                        </motion.h2>
                        <div className="features-grid">
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, x: -60 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ scale: 1.05 }}
                            >
                                <h3>🌍 Bilingual Expertise</h3>
                                <p>Our team provides seamless communication in Hebrew and English, ensuring you understand every detail of your property purchase.</p>
                            </motion.div>
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, x: 60 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                viewport={{ once: true }}
                                whileHover={{ scale: 1.05 }}
                            >
                                <h3>🏆 Premium Locations</h3>
                                <p>Exclusive access to the most sought-after properties in Tel Aviv, Jerusalem, Herzliya, Netanya, and other prime Israeli locations.</p>
                            </motion.div>
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, x: -60 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                                viewport={{ once: true }}
                                whileHover={{ scale: 1.05 }}
                            >
                                <h3>💼 Investment Guidance</h3>
                                <p>Expert advice on property investment opportunities, market trends, and potential returns in the Israeli real estate market.</p>
                            </motion.div>
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, x: 60 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.4 }}
                                viewport={{ once: true }}
                                whileHover={{ scale: 1.05 }}
                            >
                                <h3>🔒 Secure Transactions</h3>
                                <p>Complete legal support and secure transaction handling, ensuring your property purchase is smooth and protected.</p>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Testimonials Section */}
                <motion.section
                    className="testimonials-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.h2
                            initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            Success Stories
                        </motion.h2>
                        <div className="testimonials-grid">
                            <motion.div
                                className="testimonial-card"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.8, delay: 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ scale: 1.05, y: -10 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Purchased a stunning penthouse in Tel Aviv with their help. The bilingual service and market expertise made all the difference!"</p>
                                    <div className="testimonial-author">
                                        <strong>Robert & Lisa C.</strong>
                                        <span>International Investors</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                viewport={{ once: true }}
                                whileHover={{ scale: 1.05, y: -10 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Found our dream villa in Herzliya. Their knowledge of the luxury market and negotiation skills saved us significantly."</p>
                                    <div className="testimonial-author">
                                        <strong>Jonathan & Emma S.</strong>
                                        <span>Luxury Home Buyers</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                                viewport={{ once: true }}
                                whileHover={{ scale: 1.05, y: -10 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Excellent investment guidance! Our property portfolio in Israel has exceeded all expectations thanks to their expertise."</p>
                                    <div className="testimonial-author">
                                        <strong>Alexander M.</strong>
                                        <span>Property Investor</span>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Contact Form Section */}
                <motion.section
                    id="contact-form"
                    className="contact-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.h2
                            initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            Find Your Dream Property Today
                        </motion.h2>
                        <motion.p
                            className="section-subtitle"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.3 }}
                            viewport={{ once: true }}
                        >
                            Tell us about your ideal property and budget, and we'll find the perfect match for you.
                        </motion.p>

                        <motion.form
                            onSubmit={handleSubmit}
                            className="contact-form"
                            initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.4 }}
                            viewport={{ once: true }}
                        >
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">First Name *</label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        value={formData.firstName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">Last Name *</label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        value={formData.lastName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="email">Email *</label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="phoneNumber">Phone Number *</label>
                                    <input
                                        type="tel"
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        value={formData.phoneNumber}
                                        onChange={handleInputChange}
                                        placeholder="50-123-4567"
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-group">
                                <label htmlFor="message">Tell us about your ideal property</label>
                                <textarea
                                    id="message"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    rows={4}
                                    placeholder="Location preferences, property type, budget range, timeline, etc."
                                />
                            </div>

                            <div className="privacy-checkbox-group">
                                <label className="privacy-checkbox-label">
                                    <input
                                        type="checkbox"
                                        checked={privacyAccepted}
                                        onChange={(e) => setPrivacyAccepted(e.target.checked)}
                                        required
                                        className="privacy-checkbox"
                                    />
                                    <span className="checkmark"></span>
                                    <span className="privacy-text">
                                        I agree to the <a href="/privacy-policy" target="_blank" rel="noopener noreferrer">Privacy Policy</a> and
                                        consent to being contacted via phone, email, or text message regarding real estate opportunities.
                                    </span>
                                </label>
                            </div>
                            <div className="form-button-container">
                                <motion.button
                                    type="submit"
                                    className="cta-button primary"
                                    disabled={isSubmitting}
                                    whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
                                    whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                                >
                                    {isSubmitting ? "Sending..." : "Find My Dream Property"}
                                </motion.button>
                                {submitMessage && (
                                    <div className={`submit-message ${submitMessage.includes('Thank you') ? 'success' : 'error'}`}>
                                        {submitMessage}
                                    </div>
                                )}
                            </div>
                        </motion.form>
                    </div>
                </motion.section>

                {/* Final CTA Section */}
                <motion.section
                    className="final-cta-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Ready to Invest in Israeli Real Estate?</h2>
                        <p>Join successful investors who chose Israel for their property investments. Expert guidance every step of the way.</p>
                        <motion.button
                            className="cta-button secondary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            Start Your Investment Journey
                        </motion.button>
                    </div>
                </motion.section>
            </div>
        </>
    );
}
