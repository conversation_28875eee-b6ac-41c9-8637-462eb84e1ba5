import type { JSX } from "react";
import { useState } from "react";
import { motion } from "framer-motion";
import { SEO } from "../SEO/SEO";
import { LeadUser } from "../../models/LeadUser";
import leadService from "../../services/LeadService";
import "./ForSaleEn.css";

export function ForSaleEn(): JSX.Element {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        message: ""
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        setSubmitMessage("");

        try {
            const leadUser = new LeadUser(
                0, // id will be set by backend
                formData.firstName,
                formData.lastName,
                formData.email,
                formData.phoneNumber,
                formData.message,
                "for sale english"
            );

            await leadService.addLead(leadUser);
            setSubmitMessage("Thank you! We'll contact you soon.");
            setFormData({
                firstName: "",
                lastName: "",
                email: "",
                phoneNumber: "",
                message: ""
            });
        } catch (error) {
            setSubmitMessage("Something went wrong. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <SEO
                title="Luxury Properties for Sale in Israel | Premium Real Estate"
                description="Discover exclusive apartments, villas, and luxury properties for sale in Tel Aviv, Jerusalem, Haifa, and across Israel. Expert bilingual real estate services with premium listings."
                keywords="buy property Israel, luxury apartments Tel Aviv, houses for sale Jerusalem, real estate Haifa, Israel property investment, bilingual real estate agent"
                ogTitle="Luxury Properties for Sale in Israel - Premium Real Estate Services"
                ogDescription="Find your dream property in Israel with our expert bilingual team. Luxury apartments, villas, and exclusive properties across Tel Aviv, Jerusalem, and more."
                ogImage="/sale-hero.jpg"
                ogUrl="https://yourdomain.com/for-sale-en"
            />
            <div className="ForSaleEn">
                {/* Hero Section */}
                <motion.section
                    className="hero-section"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <div className="hero-content">
                        <h1>Discover Luxury Properties for Sale in Israel</h1>
                        <p className="hero-subtitle">
                            Exclusive apartments, villas, and premium properties across Tel Aviv, Jerusalem, Haifa, and beyond.
                            Expert bilingual service with access to the finest real estate opportunities in Israel.
                        </p>
                        <motion.button
                            className="cta-button primary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            Find Your Dream Property
                        </motion.button>
                    </div>
                    <div className="hero-image">
                        <img src="/api/placeholder/600/400" alt="Luxury property for sale in Israel" />
                    </div>
                </motion.section>

                {/* Services Section */}
                <motion.section
                    className="services-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Premium Property Sales Services</h2>
                        <div className="services-grid">
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">🏢</div>
                                <h3>Luxury Apartments</h3>
                                <p>Premium apartments in Tel Aviv's most prestigious neighborhoods with stunning city and sea views.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">🏰</div>
                                <h3>Exclusive Villas</h3>
                                <p>Magnificent villas and houses in prime locations across Israel, perfect for luxury living and investment.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">💎</div>
                                <h3>Investment Opportunities</h3>
                                <p>Exclusive off-market properties and investment opportunities with high potential returns.</p>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Why Choose Us Section */}
                <motion.section
                    className="why-choose-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Why Choose Our Property Sales Services?</h2>
                        <div className="features-grid">
                            <div className="feature-item">
                                <h3>🌍 Bilingual Expertise</h3>
                                <p>Our team provides seamless communication in Hebrew and English, ensuring you understand every detail of your property purchase.</p>
                            </div>
                            <div className="feature-item">
                                <h3>🏆 Premium Locations</h3>
                                <p>Exclusive access to the most sought-after properties in Tel Aviv, Jerusalem, Herzliya, Netanya, and other prime Israeli locations.</p>
                            </div>
                            <div className="feature-item">
                                <h3>💼 Investment Guidance</h3>
                                <p>Expert advice on property investment opportunities, market trends, and potential returns in the Israeli real estate market.</p>
                            </div>
                            <div className="feature-item">
                                <h3>🔒 Secure Transactions</h3>
                                <p>Complete legal support and secure transaction handling, ensuring your property purchase is smooth and protected.</p>
                            </div>
                        </div>
                    </div>
                </motion.section>

                {/* Testimonials Section */}
                <motion.section
                    className="testimonials-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Success Stories</h2>
                        <div className="testimonials-grid">
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Purchased a stunning penthouse in Tel Aviv with their help. The bilingual service and market expertise made all the difference!"</p>
                                    <div className="testimonial-author">
                                        <strong>Robert & Lisa C.</strong>
                                        <span>International Investors</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Found our dream villa in Herzliya. Their knowledge of the luxury market and negotiation skills saved us significantly."</p>
                                    <div className="testimonial-author">
                                        <strong>Jonathan & Emma S.</strong>
                                        <span>Luxury Home Buyers</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Excellent investment guidance! Our property portfolio in Israel has exceeded all expectations thanks to their expertise."</p>
                                    <div className="testimonial-author">
                                        <strong>Alexander M.</strong>
                                        <span>Property Investor</span>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Contact Form Section */}
                <motion.section
                    id="contact-form"
                    className="contact-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Find Your Dream Property Today</h2>
                        <p className="section-subtitle">Tell us about your ideal property and budget, and we'll find the perfect match for you.</p>

                        <form onSubmit={handleSubmit} className="contact-form">
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">First Name *</label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        value={formData.firstName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">Last Name *</label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        value={formData.lastName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="email">Email *</label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="phoneNumber">Phone Number *</label>
                                    <input
                                        type="tel"
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        value={formData.phoneNumber}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-group">
                                <label htmlFor="message">Tell us about your ideal property</label>
                                <textarea
                                    id="message"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    rows={4}
                                    placeholder="Location preferences, property type, budget range, timeline, etc."
                                />
                            </div>
                            <motion.button
                                type="submit"
                                className="cta-button primary"
                                disabled={isSubmitting}
                                whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
                                whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                            >
                                {isSubmitting ? "Sending..." : "Find My Dream Property"}
                            </motion.button>
                            {submitMessage && (
                                <div className={`submit-message ${submitMessage.includes('Thank you') ? 'success' : 'error'}`}>
                                    {submitMessage}
                                </div>
                            )}
                        </form>
                    </div>
                </motion.section>

                {/* Final CTA Section */}
                <motion.section
                    className="final-cta-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Ready to Invest in Israeli Real Estate?</h2>
                        <p>Join successful investors who chose Israel for their property investments. Expert guidance every step of the way.</p>
                        <motion.button
                            className="cta-button secondary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            Start Your Investment Journey
                        </motion.button>
                    </div>
                </motion.section>
            </div>
        </>
    );
}
