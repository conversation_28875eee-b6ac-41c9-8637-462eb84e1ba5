import type { JSX } from "react";
import { useState } from "react";
import { motion } from "framer-motion";
import { SEO } from "../SEO/SEO";
import { LeadUser } from "../../models/LeadUser";
import leadService from "../../services/LeadService";
import "./ForRentEn.css";

export function ForRentEn(): JSX.Element {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        message: ""
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        setSubmitMessage("");

        try {
            const leadUser = new LeadUser(
                0, // id will be set by backend
                formData.firstName,
                formData.lastName,
                formData.email,
                formData.phoneNumber,
                formData.message,
                "for rent english"
            );

            await leadService.addLead(leadUser);
            setSubmitMessage("Thank you! We'll contact you soon.");
            setFormData({
                firstName: "",
                lastName: "",
                email: "",
                phoneNumber: "",
                message: ""
            });
        } catch (error) {
            setSubmitMessage("Something went wrong. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <SEO
                title="Premium Rental Properties in Israel | Find Your Perfect Home"
                description="Discover luxury apartments and houses for rent in Tel Aviv, Jerusalem, Haifa, and across Israel. Expert bilingual service, exclusive listings, and personalized assistance."
                keywords="rent apartment Israel, luxury rentals Tel Aviv, houses for rent Jerusalem, rental properties Haifa, Israel real estate rental, bilingual real estate agent"
                ogTitle="Premium Rental Properties in Israel - Expert Real Estate Services"
                ogDescription="Find your dream rental property in Israel with our expert bilingual team. Luxury apartments, houses, and exclusive listings across Tel Aviv, Jerusalem, and more."
                ogImage="/rental-hero.jpg"
                ogUrl="https://yourdomain.com/for-rent-en"
            />
            <div className="ForRentEn">
                {/* Hero Section */}
                <motion.section
                    className="hero-section"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <div className="hero-content">
                        <h1>Find Your Perfect Rental Home in Israel</h1>
                        <p className="hero-subtitle">
                            Discover luxury apartments and houses across Tel Aviv, Jerusalem, Haifa, and beyond.
                            Expert bilingual service with exclusive access to premium rental properties.
                        </p>
                        <motion.button
                            className="cta-button primary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            Start Your Search Today
                        </motion.button>
                    </div>
                    <div className="hero-image">
                        <img src="/api/placeholder/600/400" alt="Luxury rental property in Israel" />
                    </div>
                </motion.section>

                {/* Services Section */}
                <motion.section
                    className="services-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Our Rental Services</h2>
                        <div className="services-grid">
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">🏠</div>
                                <h3>Luxury Apartments</h3>
                                <p>Premium apartments in prime locations across Israel's major cities with modern amenities and stunning views.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">🏡</div>
                                <h3>Family Houses</h3>
                                <p>Spacious family homes with gardens, perfect for those seeking comfort and privacy in residential neighborhoods.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                whileHover={{ y: -10 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="service-icon">🌟</div>
                                <h3>Exclusive Listings</h3>
                                <p>Access to off-market properties and exclusive rental opportunities not available through other channels.</p>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Why Choose Us Section */}
                <motion.section
                    className="why-choose-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Why Choose Our Rental Services?</h2>
                        <div className="features-grid">
                            <div className="feature-item">
                                <h3>🌍 Bilingual Expertise</h3>
                                <p>Our team speaks both Hebrew and English fluently, ensuring seamless communication throughout your rental journey.</p>
                            </div>
                            <div className="feature-item">
                                <h3>📍 Prime Locations</h3>
                                <p>Specializing in the most desirable neighborhoods in Tel Aviv, Jerusalem, Haifa, Netanya, and other major Israeli cities.</p>
                            </div>
                            <div className="feature-item">
                                <h3>⚡ Fast Response</h3>
                                <p>Quick property matching and viewing arrangements. We understand the competitive rental market in Israel.</p>
                            </div>
                            <div className="feature-item">
                                <h3>🤝 Personal Service</h3>
                                <p>Dedicated support from initial search to lease signing, including assistance with documentation and legal requirements.</p>
                            </div>
                        </div>
                    </div>
                </motion.section>

                {/* Testimonials Section */}
                <motion.section
                    className="testimonials-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>What Our Clients Say</h2>
                        <div className="testimonials-grid">
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Found the perfect apartment in Tel Aviv within a week! The bilingual service made everything so much easier as a new immigrant."</p>
                                    <div className="testimonial-author">
                                        <strong>Sarah M.</strong>
                                        <span>New Immigrant from USA</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Exceptional service! They helped us find a beautiful family home in Jerusalem with a garden for our children."</p>
                                    <div className="testimonial-author">
                                        <strong>David & Rachel K.</strong>
                                        <span>Family from Canada</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"Professional, responsive, and truly understood our needs. Highly recommend for anyone looking to rent in Israel!"</p>
                                    <div className="testimonial-author">
                                        <strong>Michael T.</strong>
                                        <span>Business Professional</span>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Contact Form Section */}
                <motion.section
                    id="contact-form"
                    className="contact-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Start Your Rental Search Today</h2>
                        <p className="section-subtitle">Tell us about your ideal rental property and we'll find the perfect match for you.</p>

                        <form onSubmit={handleSubmit} className="contact-form">
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">First Name *</label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        value={formData.firstName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">Last Name *</label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        value={formData.lastName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="email">Email *</label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="phoneNumber">Phone Number *</label>
                                    <input
                                        type="tel"
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        value={formData.phoneNumber}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-group">
                                <label htmlFor="message">Tell us about your ideal rental property</label>
                                <textarea
                                    id="message"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    rows={4}
                                    placeholder="Location preferences, number of bedrooms, budget range, move-in date, etc."
                                />
                            </div>
                            <motion.button
                                type="submit"
                                className="cta-button primary"
                                disabled={isSubmitting}
                                whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
                                whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                            >
                                {isSubmitting ? "Sending..." : "Find My Perfect Rental"}
                            </motion.button>
                            {submitMessage && (
                                <div className={`submit-message ${submitMessage.includes('Thank you') ? 'success' : 'error'}`}>
                                    {submitMessage}
                                </div>
                            )}
                        </form>
                    </div>
                </motion.section>

                {/* Final CTA Section */}
                <motion.section
                    className="final-cta-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>Ready to Find Your Dream Rental in Israel?</h2>
                        <p>Join hundreds of satisfied clients who found their perfect home through our expert rental services.</p>
                        <motion.button
                            className="cta-button secondary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            Contact Us Today
                        </motion.button>
                    </div>
                </motion.section>
            </div>
        </>
    );
}
