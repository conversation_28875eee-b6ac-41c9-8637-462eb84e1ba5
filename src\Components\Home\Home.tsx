import type { JSX } from "react";
import { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { SEO } from "../SEO/SEO";
import "./Home.css";

export function Home(): JSX.Element {
    const [language, setLanguage] = useState<'en' | 'he'>('en');
    const navigate = useNavigate();

    const toggleLanguage = () => {
        setLanguage(prev => prev === 'en' ? 'he' : 'en');
    };

    const content = {
        en: {
            title: "Premium Real Estate in Israel",
            subtitle: "Discover luxury properties for sale and rent across Tel Aviv, Jerusalem, Haifa, and beyond",
            services: "Our Services",
            forSale: "Properties for Sale",
            forRent: "Properties for Rent",
            forSaleDesc: "Exclusive apartments, villas, and luxury properties with expert bilingual service",
            forRentDesc: "Premium rental properties with personalized assistance and exclusive listings",
            whyChoose: "Why Choose Us?",
            bilingual: "Bilingual Expertise",
            bilingualDesc: "Seamless communication in Hebrew and English",
            exclusive: "Exclusive Access",
            exclusiveDesc: "Off-market properties and premium listings",
            expert: "Expert Guidance",
            expertDesc: "Professional advice and market insights",
            secure: "Secure Transactions",
            secureDesc: "Complete legal support and protected deals",
            getStarted: "Get Started Today",
            exploreRent: "Explore Rentals",
            exploreSale: "Explore Sales"
        },
        he: {
            title: "נדלן פרימיום בישראל",
            subtitle: "גלה נכסי יוקרה למכירה ולהשכרה ברחבי תל אביב, ירושלים, חיפה ועוד",
            services: "השירותים שלנו",
            forSale: "נכסים למכירה",
            forRent: "נכסים להשכרה",
            forSaleDesc: "דירות בלעדיות, וילות ונכסי יוקרה עם שירות דו-לשוני מקצועי",
            forRentDesc: "נכסי השכרה מובחרים עם ליווי אישי ורישומים בלעדיים",
            whyChoose: "למה לבחור בנו?",
            bilingual: "מומחיות דו-לשונית",
            bilingualDesc: "תקשורת חלקה בעברית ואנגלית",
            exclusive: "גישה בלעדית",
            exclusiveDesc: "נכסים מחוץ לשוק ורישומים פרימיום",
            expert: "ייעוץ מקצועי",
            expertDesc: "ייעוץ מקצועי ותובנות שוק",
            secure: "עסקאות מאובטחות",
            secureDesc: "תמיכה משפטית מלאה ועסקאות מוגנות",
            getStarted: "התחל היום",
            exploreRent: "חקור השכרות",
            exploreSale: "חקור מכירות"
        }
    };

    const currentContent = content[language];

    return (
        <>
            <SEO
                title={language === 'en' ?
                    "Premium Real Estate Israel | Luxury Properties Sale & Rent" :
                    "נדלן פרימיום ישראל | נכסי יוקרה למכירה והשכרה"
                }
                description={language === 'en' ?
                    "Discover luxury real estate in Israel. Expert bilingual services for property sales and rentals in Tel Aviv, Jerusalem, Haifa. Premium apartments, villas, and exclusive listings." :
                    "גלה נדלן יוקרה בישראל. שירותים דו-לשוניים מקצועיים למכירת והשכרת נכסים בתל אביב, ירושלים, חיפה. דירות פרימיום, וילות ורישומים בלעדיים."
                }
                keywords={language === 'en' ?
                    "Israel real estate, luxury properties Tel Aviv, Jerusalem apartments, Haifa villas, property investment Israel, bilingual real estate agent, premium rentals" :
                    "נדלן ישראל, נכסי יוקרה תל אביב, דירות ירושלים, וילות חיפה, השקעות נדלן ישראל, מתווך דו לשוני, השכרות פרימיום"
                }
                ogTitle={currentContent.title}
                ogDescription={currentContent.subtitle}
                ogImage="/home-hero.jpg"
                ogUrl="https://yourdomain.com"
            />
            <div className={`Home ${language === 'he' ? 'rtl' : 'ltr'}`} dir={language === 'he' ? 'rtl' : 'ltr'}>
                {/* Language Switcher */}
                <motion.div
                    className="language-switcher"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <button
                        onClick={toggleLanguage}
                        className={`lang-button ${language === 'en' ? 'active' : ''}`}
                    >
                        English
                    </button>
                    <button
                        onClick={toggleLanguage}
                        className={`lang-button ${language === 'he' ? 'active' : ''}`}
                    >
                        עברית
                    </button>
                </motion.div>

                {/* Hero Section */}
                <motion.section
                    className="hero-section"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <div className="hero-content">
                        <motion.h1
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                        >
                            {currentContent.title}
                        </motion.h1>
                        <motion.p
                            className="hero-subtitle"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.4 }}
                        >
                            {currentContent.subtitle}
                        </motion.p>
                        <motion.div
                            className="hero-buttons"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.6 }}
                        >
                            <motion.button
                                className="cta-button primary"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => navigate(language === 'en' ? '/for-sale-en' : '/for-sale-he')}
                            >
                                {currentContent.exploreSale}
                            </motion.button>
                            <motion.button
                                className="cta-button secondary"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => navigate(language === 'en' ? '/for-rent-en' : '/for-rent-he')}
                            >
                                {currentContent.exploreRent}
                            </motion.button>
                        </motion.div>
                    </div>
                    <motion.div
                        className="hero-image"
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                    >
                        <img src="/api/placeholder/600/400" alt="Luxury real estate in Israel" />
                    </motion.div>
                </motion.section>

                {/* Services Section */}
                <motion.section
                    className="services-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.h2
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                        >
                            {currentContent.services}
                        </motion.h2>
                        <div className="services-grid">
                            <motion.div
                                className="service-card sale"
                                initial={{ opacity: 0, y: 50 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -10, scale: 1.02 }}
                                onClick={() => navigate(language === 'en' ? '/for-sale-en' : '/for-sale-he')}
                            >
                                <div className="service-icon">🏢</div>
                                <h3>{currentContent.forSale}</h3>
                                <p>{currentContent.forSaleDesc}</p>
                                <div className="service-arrow">→</div>
                            </motion.div>
                            <motion.div
                                className="service-card rent"
                                initial={{ opacity: 0, y: 50 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -10, scale: 1.02 }}
                                onClick={() => navigate(language === 'en' ? '/for-rent-en' : '/for-rent-he')}
                            >
                                <div className="service-icon">🏠</div>
                                <h3>{currentContent.forRent}</h3>
                                <p>{currentContent.forRentDesc}</p>
                                <div className="service-arrow">→</div>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Why Choose Us Section */}
                <motion.section
                    className="why-choose-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.h2
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                        >
                            {currentContent.whyChoose}
                        </motion.h2>
                        <div className="features-grid">
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, y: 50 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.1 }}
                                viewport={{ once: true }}
                            >
                                <div className="feature-icon">🌍</div>
                                <h3>{currentContent.bilingual}</h3>
                                <p>{currentContent.bilingualDesc}</p>
                            </motion.div>
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, y: 50 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                viewport={{ once: true }}
                            >
                                <div className="feature-icon">🔑</div>
                                <h3>{currentContent.exclusive}</h3>
                                <p>{currentContent.exclusiveDesc}</p>
                            </motion.div>
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, y: 50 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                viewport={{ once: true }}
                            >
                                <div className="feature-icon">💼</div>
                                <h3>{currentContent.expert}</h3>
                                <p>{currentContent.expertDesc}</p>
                            </motion.div>
                            <motion.div
                                className="feature-item"
                                initial={{ opacity: 0, y: 50 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                viewport={{ once: true }}
                            >
                                <div className="feature-icon">🔒</div>
                                <h3>{currentContent.secure}</h3>
                                <p>{currentContent.secureDesc}</p>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>
            </div>
        </>
    );
}
