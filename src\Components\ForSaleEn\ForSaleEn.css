/* ForSaleEn Component Styles */
.ForSaleEn {
    width: 100%;
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.ForSaleEn .hero-section,
.hero-section {
    background: linear-gradient(-45deg, #2c3e50, #34495e, #2c3e50, #34495e);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    min-height: 80vh;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.9;
}

.hero-image {
    flex: 1;
    max-width: 600px;
    margin: 2rem auto 0;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: float 6s ease-in-out infinite;
}

/* Buttons */
.cta-button {
    padding: 18px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 200px;
    position: relative;
    overflow: hidden;
    margin: 10px 0;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button.primary {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.4);
    border: 2px solid transparent;
}

.cta-button.primary:hover {
    box-shadow: 0 15px 40px rgba(231, 76, 60, 0.5);
    transform: translateY(-3px) scale(1.02);
}

.cta-button.secondary {
    background: transparent;
    color: #2c3e50;
    border: 2px solid #2c3e50;
    backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
    background: #2c3e50;
    color: white;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(44, 62, 80, 0.3);
}

.cta-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.cta-button:active {
    transform: translateY(-1px) scale(0.98);
}

/* Services Section */
.services-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.services-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.service-card:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.service-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Why Choose Section */
.why-choose-section {
    padding: 100px 0;
    background: white;
}

.why-choose-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    padding: 2rem;
    border-left: 4px solid #e74c3c;
    background: #f8f9fa;
    border-radius: 10px;
}

.feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.feature-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.testimonials-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.testimonial-author strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.testimonial-author span {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Contact Section */
.ForSaleEn .contact-section,
.contact-section {
    padding: 100px 0;
    background: white !important;
}

.contact-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-form {
    max-width: 700px;
    margin: 0 auto;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.form-group label {
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.form-group input,
.form-group textarea {
    padding: 16px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    transform: translateY(-2px);
}

.form-group textarea {
    resize: vertical;
    min-height: 140px;
    font-family: inherit;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
    opacity: 1;
}

.submit-message {
    margin-top: 1.5rem;
    padding: 16px 24px;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    font-size: 1rem;
    animation: slideInUp 0.3s ease-out;
}

.submit-message.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.submit-message.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Privacy Checkbox */
.privacy-checkbox-group {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.privacy-checkbox-group:hover {
    border-color: #e74c3c;
    background: #fff5f5;
}

.privacy-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.5;
    color: #2c3e50;
}

.privacy-checkbox {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e74c3c;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.privacy-checkbox:checked + .checkmark {
    background: #e74c3c;
    border-color: #e74c3c;
}

.privacy-checkbox:checked + .checkmark::after {
    content: "✓";
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.privacy-text {
    flex: 1;
}

.privacy-text a {
    color: #e74c3c;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.privacy-text a:hover {
    color: #c0392b;
    text-decoration: underline;
}

/* Form Button Container */
.form-button-container {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
}

/* Final CTA Section */
.final-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    text-align: center;
}

.final-cta-section h2 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.final-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .hero-section {
        padding: 60px 0;
        min-height: 70vh;
        flex-direction: column;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-image {
        margin-top: 2rem;
        max-width: 100%;
    }

    .services-section,
    .why-choose-section,
    .testimonials-section,
    .contact-section,
    .final-cta-section {
        padding: 60px 0;
    }

    .services-section h2,
    .why-choose-section h2,
    .testimonials-section h2,
    .contact-section h2,
    .final-cta-section h2 {
        font-size: 2.2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .services-section h2,
    .why-choose-section h2,
    .testimonials-section h2,
    .contact-section h2,
    .final-cta-section h2 {
        font-size: 1.8rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1.5rem;
    }

    .feature-item {
        padding: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.service-card,
.feature-item,
.testimonial-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
.cta-button:focus,
.form-group input:focus,
.form-group textarea:focus {
    outline: 2px solid #e74c3c;
    outline-offset: 2px;
}

/* Gradient Animation */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Float Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
}

/* Print Styles */
@media print {
    .hero-section,
    .final-cta-section {
        background: white !important;
        color: black !important;
    }

    .cta-button {
        display: none;
    }
}
