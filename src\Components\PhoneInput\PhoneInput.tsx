import type { JSX } from "react";
import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { countryCodes } from "../../utils/countryCodes";
import "./PhoneInput.css";

interface CountryCode {
    name: string;
    code: string;
    dialCode: string;
    flag: string;
}

interface PhoneInputProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    required?: boolean;
    id?: string;
    name?: string;
    label?: string;
    isRTL?: boolean;
}

export function PhoneInput({
    value,
    onChange,
    placeholder = "50-123-4567",
    required = false,
    id = "phoneNumber",
    name = "phoneNumber",
    label = "Phone Number",
    isRTL = false
}: PhoneInputProps): JSX.Element {
    const [selectedCountry, setSelectedCountry] = useState<CountryCode>(
        countryCodes.find(c => c.code === "IL") || countryCodes[0]
    );
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const handleCountrySelect = (country: CountryCode) => {
        setSelectedCountry(country);
        setIsDropdownOpen(false);
        if (inputRef.current) {
            inputRef.current.focus();
        }
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const phoneNumber = e.target.value;
        // Combine country code with phone number
        const fullNumber = `${selectedCountry.dialCode} ${phoneNumber}`;
        onChange(fullNumber);
    };

    // Extract phone number without country code for display
    const phoneNumberOnly = value.replace(selectedCountry.dialCode, "").trim();

    return (
        <div className={`phone-input-container ${isRTL ? 'rtl' : 'ltr'}`}>
            {label && (
                <label htmlFor={id} className="phone-input-label">
                    {label} {required && "*"}
                </label>
            )}
            <div className="phone-input-wrapper">
                <div className="country-selector" ref={dropdownRef}>
                    <button
                        type="button"
                        className="country-button"
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        aria-label="Select country code"
                    >
                        <span className="country-flag">{selectedCountry.flag}</span>
                        <span className="country-code">{selectedCountry.dialCode}</span>
                        <motion.span
                            className="dropdown-arrow"
                            animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                        >
                            ▼
                        </motion.span>
                    </button>

                    <AnimatePresence>
                        {isDropdownOpen && (
                            <motion.div
                                className="country-dropdown"
                                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                transition={{ duration: 0.2 }}
                            >
                                <div className="countries-list">
                                    {countryCodes.map((country: CountryCode) => (
                                        <motion.button
                                            key={country.code}
                                            type="button"
                                            className={`country-option ${selectedCountry.code === country.code ? 'selected' : ''}`}
                                            onClick={() => handleCountrySelect(country)}
                                            whileHover={{ backgroundColor: "#f8f9fa" }}
                                            whileTap={{ scale: 0.98 }}
                                        >
                                            <span className="country-flag">{country.flag}</span>
                                            <span className="country-name">{country.name}</span>
                                            <span className="country-dial-code">{country.dialCode}</span>
                                        </motion.button>
                                    ))}
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>

                <input
                    ref={inputRef}
                    type="tel"
                    id={id}
                    name={name}
                    value={phoneNumberOnly}
                    onChange={handlePhoneChange}
                    placeholder={placeholder}
                    required={required}
                    className="phone-number-input"
                />
            </div>
        </div>
    );
}
