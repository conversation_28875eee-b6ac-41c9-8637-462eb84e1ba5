import type { JSX } from "react";
import { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { countryCodes } from "../../utils/countryCodes";
import "./PhoneInput.css";

interface CountryCode {
    name: string;
    code: string;
    dialCode: string;
    flag: string;
}

interface PhoneInputProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    required?: boolean;
    id?: string;
    name?: string;
    label?: string;
    isRTL?: boolean;
}

export function PhoneInput({
    value,
    onChange,
    placeholder = "50-123-4567",
    required = false,
    id = "phoneNumber",
    name = "phoneNumber",
    label = "Phone Number",
    isRTL = false
}: PhoneInputProps): JSX.Element {
    const [selectedCountry, setSelectedCountry] = useState<CountryCode>(
        countryCodes.find(c => c.code === "IL") || countryCodes[0]
    );
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const handleCountrySelect = (country: CountryCode) => {
        setSelectedCountry(country);
        setIsDropdownOpen(false);
        if (inputRef.current) {
            inputRef.current.focus();
        }
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const phoneNumber = e.target.value;
        // Combine country code with phone number
        const fullNumber = `${selectedCountry.dialCode} ${phoneNumber}`;
        onChange(fullNumber);
    };

    // Extract phone number without country code for display
    const phoneNumberOnly = value.replace(selectedCountry.dialCode, "").trim();

    return (
        <div className={`phone-input-container ${isRTL ? 'rtl' : 'ltr'}`}>
            {label && (
                <label htmlFor={id} className="phone-input-label">
                    {label} {required && "*"}
                </label>
            )}
            {/* Debug indicator */}
            {isDropdownOpen && <div style={{position: 'fixed', top: 0, left: 0, background: 'red', color: 'white', padding: '5px', zIndex: 99999}}>Dropdown Open!</div>}
            <div className="phone-input-wrapper">
                <div className="country-selector" ref={dropdownRef}>
                    <button
                        type="button"
                        className="country-button"
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('Country button clicked, current state:', isDropdownOpen);
                            setIsDropdownOpen(!isDropdownOpen);
                        }}
                        aria-label="Select country code"
                    >
                        <span className="country-flag">{selectedCountry.flag}</span>
                        <span className="country-code">{selectedCountry.dialCode}</span>
                        <motion.span
                            className="dropdown-arrow"
                            animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                        >
                            ▼
                        </motion.span>
                    </button>

                    {isDropdownOpen && (
                        <div
                            style={{
                                position: 'fixed',
                                zIndex: 99999,
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                background: 'white',
                                border: '2px solid #667eea',
                                borderRadius: '12px',
                                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                                maxHeight: '400px',
                                width: '90vw',
                                maxWidth: '500px',
                                overflow: 'hidden'
                            }}
                        >
                            <div style={{
                                padding: '16px',
                                borderBottom: '1px solid #e9ecef',
                                background: '#f8f9fa',
                                fontWeight: 'bold',
                                textAlign: 'center'
                            }}>
                                Select Country Code
                            </div>
                            <div style={{
                                maxHeight: '300px',
                                overflowY: 'auto',
                                padding: '8px'
                            }}>
                                {countryCodes.map((country: CountryCode) => (
                                    <button
                                        key={country.code}
                                        type="button"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            console.log('Country selected:', country.name);
                                            handleCountrySelect(country);
                                        }}
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            width: '100%',
                                            padding: '12px 16px',
                                            border: 'none',
                                            background: selectedCountry.code === country.code ? '#667eea' : 'white',
                                            color: selectedCountry.code === country.code ? 'white' : '#2c3e50',
                                            cursor: 'pointer',
                                            borderRadius: '8px',
                                            margin: '2px 0',
                                            fontSize: '14px',
                                            transition: 'all 0.2s ease'
                                        }}
                                        onMouseEnter={(e) => {
                                            if (selectedCountry.code !== country.code) {
                                                e.currentTarget.style.background = '#f8f9fa';
                                            }
                                        }}
                                        onMouseLeave={(e) => {
                                            if (selectedCountry.code !== country.code) {
                                                e.currentTarget.style.background = 'white';
                                            }
                                        }}
                                    >
                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                            <span style={{ fontSize: '18px' }}>{country.flag}</span>
                                            <span style={{ fontWeight: '500' }}>{country.name}</span>
                                        </div>
                                        <span style={{ fontWeight: 'bold', minWidth: '60px', textAlign: 'right' }}>
                                            {country.dialCode}
                                        </span>
                                    </button>
                                ))}
                            </div>
                            <div style={{
                                padding: '12px',
                                borderTop: '1px solid #e9ecef',
                                textAlign: 'center'
                            }}>
                                <button
                                    type="button"
                                    onClick={() => setIsDropdownOpen(false)}
                                    style={{
                                        padding: '8px 24px',
                                        background: '#6c757d',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '6px',
                                        cursor: 'pointer',
                                        fontSize: '14px'
                                    }}
                                >
                                    Close
                                </button>
                            </div>
                        </div>
                    )}
                </div>

                <input
                    ref={inputRef}
                    type="tel"
                    id={id}
                    name={name}
                    value={phoneNumberOnly}
                    onChange={handlePhoneChange}
                    placeholder={placeholder}
                    required={required}
                    className="phone-number-input"
                />
            </div>
        </div>
    );
}
