import type { JSX } from "react";
import { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { countryCodes } from "../../utils/countryCodes";
import "./PhoneInput.css";

interface CountryCode {
    name: string;
    code: string;
    dialCode: string;
    flag: string;
}

interface PhoneInputProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    required?: boolean;
    id?: string;
    name?: string;
    label?: string;
    isRTL?: boolean;
}

export function PhoneInput({
    value,
    onChange,
    placeholder = "50-123-4567",
    required = false,
    id = "phoneNumber",
    name = "phoneNumber",
    label = "Phone Number",
    isRTL = false
}: PhoneInputProps): JSX.Element {
    const [selectedCountry, setSelectedCountry] = useState<CountryCode>(
        countryCodes.find(c => c.code === "IL") || countryCodes[0]
    );
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const handleCountrySelect = (country: CountryCode) => {
        setSelectedCountry(country);
        setIsDropdownOpen(false);
        if (inputRef.current) {
            inputRef.current.focus();
        }
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const phoneNumber = e.target.value;
        // Combine country code with phone number
        const fullNumber = `${selectedCountry.dialCode} ${phoneNumber}`;
        onChange(fullNumber);
    };

    // Extract phone number without country code for display
    const phoneNumberOnly = value.replace(selectedCountry.dialCode, "").trim();

    return (
        <div className={`phone-input-container ${isRTL ? 'rtl' : 'ltr'}`}>
            {label && (
                <label htmlFor={id} className="phone-input-label">
                    {label} {required && "*"}
                </label>
            )}
            {/* Debug indicator */}
            {isDropdownOpen && <div style={{position: 'fixed', top: 0, left: 0, background: 'red', color: 'white', padding: '5px', zIndex: 99999}}>Dropdown Open!</div>}
            <div className="phone-input-wrapper">
                <div className="country-selector" ref={dropdownRef}>
                    <button
                        type="button"
                        className="country-button"
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('Country button clicked, current state:', isDropdownOpen);
                            setIsDropdownOpen(!isDropdownOpen);
                        }}
                        aria-label="Select country code"
                    >
                        <span className="country-flag">{selectedCountry.flag}</span>
                        <span className="country-code">{selectedCountry.dialCode}</span>
                        <motion.span
                            className="dropdown-arrow"
                            animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                        >
                            ▼
                        </motion.span>
                    </button>

                    {isDropdownOpen && (
                        <div
                            className="country-dropdown"
                            style={{
                                position: 'absolute',
                                zIndex: 9999,
                                top: '100%',
                                left: 0,
                                right: 0,
                                background: 'white',
                                border: '2px solid #e9ecef',
                                borderRadius: '12px',
                                boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
                                maxHeight: '280px',
                                overflow: 'hidden',
                                marginTop: '4px',
                                minWidth: '300px'
                            }}
                        >
                            <div className="countries-list">
                                {countryCodes.map((country: CountryCode) => (
                                    <button
                                        key={country.code}
                                        type="button"
                                        className={`country-option ${selectedCountry.code === country.code ? 'selected' : ''}`}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            console.log('Country selected:', country.name);
                                            handleCountrySelect(country);
                                        }}
                                    >
                                        <span className="country-flag">{country.flag}</span>
                                        <span className="country-name">{country.name}</span>
                                        <span className="country-dial-code">{country.dialCode}</span>
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                <input
                    ref={inputRef}
                    type="tel"
                    id={id}
                    name={name}
                    value={phoneNumberOnly}
                    onChange={handlePhoneChange}
                    placeholder={placeholder}
                    required={required}
                    className="phone-number-input"
                />
            </div>
        </div>
    );
}
