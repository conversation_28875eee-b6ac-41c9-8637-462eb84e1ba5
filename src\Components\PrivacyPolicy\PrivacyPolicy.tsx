import type { JSX } from "react";
import { motion } from "framer-motion";
import { SEO } from "../SEO/SEO";
import "./PrivacyPolicy.css";

export function PrivacyPolicy(): JSX.Element {
    return (
        <>
            <SEO
                title="Privacy Policy | Real Estate Services Israel"
                description="Privacy policy for our real estate services. Learn how we protect your personal information and use your data to provide the best property services in Israel."
                keywords="privacy policy, data protection, real estate privacy, Israel property services, personal information"
                ogTitle="Privacy Policy - Real Estate Services Israel"
                ogDescription="Our commitment to protecting your privacy and personal information in our real estate services."
                ogImage="/privacy-policy.jpg"
                ogUrl="https://yourdomain.com/privacy-policy"
            />
            <div className="PrivacyPolicy">
                <motion.section 
                    className="privacy-hero"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <div className="container">
                        <h1>Privacy Policy</h1>
                        <p className="hero-subtitle">
                            Your privacy is important to us. This policy explains how we collect, use, and protect your personal information.
                        </p>
                    </div>
                </motion.section>

                <motion.section 
                    className="privacy-content"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.div 
                            className="policy-section"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                            viewport={{ once: true }}
                        >
                            <h2>Information We Collect</h2>
                            <p>
                                When you submit our contact form, we collect the following personal information:
                            </p>
                            <ul>
                                <li><strong>Name:</strong> Your first and last name to personalize our communication</li>
                                <li><strong>Email Address:</strong> To send you property listings and respond to your inquiries</li>
                                <li><strong>Phone Number:</strong> To contact you directly about real estate opportunities</li>
                                <li><strong>Message/Requirements:</strong> Your specific property needs and preferences</li>
                            </ul>
                        </motion.div>

                        <motion.div 
                            className="policy-section"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            <h2>How We Use Your Information</h2>
                            <p>
                                By submitting our contact form, you agree that we may use your personal information to:
                            </p>
                            <ul>
                                <li><strong>Contact You:</strong> We will reach out via phone calls, emails, or text messages</li>
                                <li><strong>Property Opportunities:</strong> Share relevant real estate listings and investment opportunities</li>
                                <li><strong>Market Updates:</strong> Provide updates about the Israeli real estate market</li>
                                <li><strong>Personalized Service:</strong> Tailor our services to match your specific property needs</li>
                                <li><strong>Follow-up Communication:</strong> Maintain ongoing communication about your property search</li>
                            </ul>
                        </motion.div>

                        <motion.div 
                            className="policy-section"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                            viewport={{ once: true }}
                        >
                            <h2>Your Consent</h2>
                            <div className="consent-box">
                                <p>
                                    <strong>By checking the privacy policy agreement checkbox and submitting our form, you explicitly consent to:</strong>
                                </p>
                                <ul>
                                    <li>Receiving phone calls from our real estate team</li>
                                    <li>Receiving emails about property opportunities and market updates</li>
                                    <li>Receiving text messages regarding urgent property matters</li>
                                    <li>Being contacted about real estate opportunities that match your criteria</li>
                                    <li>Ongoing communication throughout your property search process</li>
                                </ul>
                            </div>
                        </motion.div>

                        <motion.div 
                            className="policy-section"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                            viewport={{ once: true }}
                        >
                            <h2>Data Protection</h2>
                            <p>
                                We are committed to protecting your personal information:
                            </p>
                            <ul>
                                <li>Your data is stored securely and accessed only by authorized personnel</li>
                                <li>We do not sell or share your information with third parties without consent</li>
                                <li>We use industry-standard security measures to protect your data</li>
                                <li>You can request to update or delete your information at any time</li>
                            </ul>
                        </motion.div>

                        <motion.div 
                            className="policy-section"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.5 }}
                            viewport={{ once: true }}
                        >
                            <h2>Your Rights</h2>
                            <p>You have the right to:</p>
                            <ul>
                                <li>Request access to your personal data</li>
                                <li>Request correction of inaccurate data</li>
                                <li>Request deletion of your data</li>
                                <li>Opt-out of marketing communications at any time</li>
                                <li>Withdraw your consent for future communications</li>
                            </ul>
                        </motion.div>

                        <motion.div 
                            className="policy-section contact-section"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.6 }}
                            viewport={{ once: true }}
                        >
                            <h2>Contact Us</h2>
                            <p>
                                If you have any questions about this privacy policy or wish to exercise your rights, 
                                please contact us at:
                            </p>
                            <div className="contact-info">
                                <p><strong>Email:</strong> <EMAIL></p>
                                <p><strong>Phone:</strong> +972-XX-XXX-XXXX</p>
                            </div>
                        </motion.div>

                        <motion.div 
                            className="policy-section"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.7 }}
                            viewport={{ once: true }}
                        >
                            <h2>Updates to This Policy</h2>
                            <p>
                                We may update this privacy policy from time to time. Any changes will be posted on this page 
                                with an updated effective date.
                            </p>
                            <p><strong>Last Updated:</strong> {new Date().toLocaleDateString()}</p>
                        </motion.div>
                    </div>
                </motion.section>
            </div>
        </>
    );
}
