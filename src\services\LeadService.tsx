import axios from "axios";
import type { LeadUser } from "../models/LeadUser";

class LeadService{
    private readonly baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8080';

    async addLead(leadUser: LeadUser) {
        try {
            return (await axios.post(`${this.baseUrl}/api/lead`, leadUser)).data;
        } catch (error) {
            throw new Error('Failed to submit lead. Please try again.');
        }
    }
}

const leadService = new LeadService;
export default leadService;