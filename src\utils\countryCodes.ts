export interface CountryCode {
    name: string;
    code: string;
    dialCode: string;
    flag: string;
}

export const countryCodes: CountryCode[] = [
    { name: "Israel", code: "IL", dialCode: "+972", flag: "🇮🇱" },
    { name: "United States", code: "US", dialCode: "+1", flag: "🇺🇸" },
    { name: "United Kingdom", code: "GB", dialCode: "+44", flag: "🇬🇧" },
    { name: "Canada", code: "CA", dialCode: "+1", flag: "🇨🇦" },
    { name: "Australia", code: "AU", dialCode: "+61", flag: "🇦🇺" },
    { name: "Germany", code: "DE", dialCode: "+49", flag: "🇩🇪" },
    { name: "France", code: "FR", dialCode: "+33", flag: "🇫🇷" },
    { name: "Italy", code: "IT", dialCode: "+39", flag: "🇮🇹" },
    { name: "Spain", code: "ES", dialCode: "+34", flag: "🇪🇸" },
    { name: "Netherlands", code: "NL", dialCode: "+31", flag: "🇳🇱" },
    { name: "Belgium", code: "BE", dialCode: "+32", flag: "🇧🇪" },
    { name: "Switzerland", code: "CH", dialCode: "+41", flag: "🇨🇭" },
    { name: "Austria", code: "AT", dialCode: "+43", flag: "🇦🇹" },
    { name: "Sweden", code: "SE", dialCode: "+46", flag: "🇸🇪" },
    { name: "Norway", code: "NO", dialCode: "+47", flag: "🇳🇴" },
    { name: "Denmark", code: "DK", dialCode: "+45", flag: "🇩🇰" },
    { name: "Finland", code: "FI", dialCode: "+358", flag: "🇫🇮" },
    { name: "Poland", code: "PL", dialCode: "+48", flag: "🇵🇱" },
    { name: "Czech Republic", code: "CZ", dialCode: "+420", flag: "🇨🇿" },
    { name: "Hungary", code: "HU", dialCode: "+36", flag: "🇭🇺" },
    { name: "Romania", code: "RO", dialCode: "+40", flag: "🇷🇴" },
    { name: "Bulgaria", code: "BG", dialCode: "+359", flag: "🇧🇬" },
    { name: "Greece", code: "GR", dialCode: "+30", flag: "🇬🇷" },
    { name: "Turkey", code: "TR", dialCode: "+90", flag: "🇹🇷" },
    { name: "Russia", code: "RU", dialCode: "+7", flag: "🇷🇺" },
    { name: "Ukraine", code: "UA", dialCode: "+380", flag: "🇺🇦" },
    { name: "Belarus", code: "BY", dialCode: "+375", flag: "🇧🇾" },
    { name: "Lithuania", code: "LT", dialCode: "+370", flag: "🇱🇹" },
    { name: "Latvia", code: "LV", dialCode: "+371", flag: "🇱🇻" },
    { name: "Estonia", code: "EE", dialCode: "+372", flag: "🇪🇪" },
    { name: "China", code: "CN", dialCode: "+86", flag: "🇨🇳" },
    { name: "Japan", code: "JP", dialCode: "+81", flag: "🇯🇵" },
    { name: "South Korea", code: "KR", dialCode: "+82", flag: "🇰🇷" },
    { name: "India", code: "IN", dialCode: "+91", flag: "🇮🇳" },
    { name: "Singapore", code: "SG", dialCode: "+65", flag: "🇸🇬" },
    { name: "Hong Kong", code: "HK", dialCode: "+852", flag: "🇭🇰" },
    { name: "Thailand", code: "TH", dialCode: "+66", flag: "🇹🇭" },
    { name: "Malaysia", code: "MY", dialCode: "+60", flag: "🇲🇾" },
    { name: "Philippines", code: "PH", dialCode: "+63", flag: "🇵🇭" },
    { name: "Indonesia", code: "ID", dialCode: "+62", flag: "🇮🇩" },
    { name: "Vietnam", code: "VN", dialCode: "+84", flag: "🇻🇳" },
    { name: "Brazil", code: "BR", dialCode: "+55", flag: "🇧🇷" },
    { name: "Argentina", code: "AR", dialCode: "+54", flag: "🇦🇷" },
    { name: "Chile", code: "CL", dialCode: "+56", flag: "🇨🇱" },
    { name: "Colombia", code: "CO", dialCode: "+57", flag: "🇨🇴" },
    { name: "Mexico", code: "MX", dialCode: "+52", flag: "🇲🇽" },
    { name: "South Africa", code: "ZA", dialCode: "+27", flag: "🇿🇦" },
    { name: "Egypt", code: "EG", dialCode: "+20", flag: "🇪🇬" },
    { name: "Morocco", code: "MA", dialCode: "+212", flag: "🇲🇦" },
    { name: "Nigeria", code: "NG", dialCode: "+234", flag: "🇳🇬" },
    { name: "Kenya", code: "KE", dialCode: "+254", flag: "🇰🇪" },
    { name: "Ghana", code: "GH", dialCode: "+233", flag: "🇬🇭" },
    { name: "UAE", code: "AE", dialCode: "+971", flag: "🇦🇪" },
    { name: "Saudi Arabia", code: "SA", dialCode: "+966", flag: "🇸🇦" },
    { name: "Qatar", code: "QA", dialCode: "+974", flag: "🇶🇦" },
    { name: "Kuwait", code: "KW", dialCode: "+965", flag: "🇰🇼" },
    { name: "Bahrain", code: "BH", dialCode: "+973", flag: "🇧🇭" },
    { name: "Oman", code: "OM", dialCode: "+968", flag: "🇴🇲" },
    { name: "Jordan", code: "JO", dialCode: "+962", flag: "🇯🇴" },
    { name: "Lebanon", code: "LB", dialCode: "+961", flag: "🇱🇧" },
    { name: "Cyprus", code: "CY", dialCode: "+357", flag: "🇨🇾" },
    { name: "Malta", code: "MT", dialCode: "+356", flag: "🇲🇹" },
    { name: "Iceland", code: "IS", dialCode: "+354", flag: "🇮🇸" },
    { name: "Ireland", code: "IE", dialCode: "+353", flag: "🇮🇪" },
    { name: "Portugal", code: "PT", dialCode: "+351", flag: "🇵🇹" },
    { name: "Luxembourg", code: "LU", dialCode: "+352", flag: "🇱🇺" },
    { name: "Monaco", code: "MC", dialCode: "+377", flag: "🇲🇨" },
    { name: "Andorra", code: "AD", dialCode: "+376", flag: "🇦🇩" },
    { name: "San Marino", code: "SM", dialCode: "+378", flag: "🇸🇲" },
    { name: "Vatican City", code: "VA", dialCode: "+379", flag: "🇻🇦" },
    { name: "Liechtenstein", code: "LI", dialCode: "+423", flag: "🇱🇮" },
    { name: "New Zealand", code: "NZ", dialCode: "+64", flag: "🇳🇿" },
    { name: "Fiji", code: "FJ", dialCode: "+679", flag: "🇫🇯" }
];

export const getCountryByCode = (code: string): CountryCode | undefined => {
    return countryCodes.find(country => country.code === code);
};

export const getCountryByDialCode = (dialCode: string): CountryCode | undefined => {
    return countryCodes.find(country => country.dialCode === dialCode);
};
