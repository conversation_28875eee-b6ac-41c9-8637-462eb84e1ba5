/* ForRentHE Component Styles - Hebrew RTL */
.ForRentHE {
    width: 100%;
    min-height: 100vh;
    direction: rtl;
    font-family: '<PERSON>pins', 'Arial', sans-serif;
}

/* Container */
.ForRentHE .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.ForRentHE .hero-section {
    background: linear-gradient(-45deg, #667eea, #764ba2, #667eea, #764ba2);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    min-height: 80vh;
    direction: rtl;
}

.ForRentHE .hero-content {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.ForRentHE .hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.ForRentHE .hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.9;
}

.ForRentHE .hero-image {
    flex: 1;
    max-width: 600px;
    margin: 2rem auto 0;
}

.ForRentHE .hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: float 6s ease-in-out infinite;
}

/* Buttons */
.ForRentHE .cta-button {
    padding: 18px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 200px;
    position: relative;
    overflow: hidden;
    margin: 10px 0;
}

.ForRentHE .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: right 0.5s;
}

.ForRentHE .cta-button:hover::before {
    right: 100%;
}

.ForRentHE .cta-button.primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
    border: 2px solid transparent;
}

.ForRentHE .cta-button.primary:hover {
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.5);
    transform: translateY(-3px) scale(1.02);
}

.ForRentHE .cta-button.secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    backdrop-filter: blur(10px);
}

.ForRentHE .cta-button.secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
}

.ForRentHE .cta-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.ForRentHE .cta-button:active {
    transform: translateY(-1px) scale(0.98);
}

/* Services Section */
.ForRentHE .services-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForRentHE .services-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForRentHE .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForRentHE .service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out;
}

.ForRentHE .service-card:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.ForRentHE .service-card:nth-child(1) {
    animation-delay: 0.1s;
}

.ForRentHE .service-card:nth-child(2) {
    animation-delay: 0.2s;
}

.ForRentHE .service-card:nth-child(3) {
    animation-delay: 0.3s;
}

.ForRentHE .service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    animation: float 4s ease-in-out infinite;
}

.ForRentHE .service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForRentHE .service-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Why Choose Section */
.ForRentHE .why-choose-section {
    padding: 100px 0;
    background: white;
}

.ForRentHE .why-choose-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForRentHE .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForRentHE .feature-item {
    padding: 2rem;
    border-right: 4px solid #667eea;
    border-left: none;
    background: #f8f9fa;
    border-radius: 10px;
}

.ForRentHE .feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForRentHE .feature-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* Testimonials Section */
.ForRentHE .testimonials-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForRentHE .testimonials-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForRentHE .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForRentHE .testimonial-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ForRentHE .testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.ForRentHE .testimonial-author strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.ForRentHE .testimonial-author span {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Contact Section */
.ForRentHE .contact-section {
    padding: 100px 0;
    background: white;
}

.ForRentHE .contact-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForRentHE .section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.ForRentHE .contact-form {
    max-width: 700px;
    margin: 0 auto;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.ForRentHE .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.ForRentHE .form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.ForRentHE .form-group label {
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
    font-size: 1rem;
}

.ForRentHE .form-group input,
.ForRentHE .form-group textarea {
    padding: 16px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: right;
    direction: rtl;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.ForRentHE .form-group input:focus,
.ForRentHE .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.ForRentHE .form-group textarea {
    resize: vertical;
    min-height: 140px;
    font-family: inherit;
}

.ForRentHE .form-group input::placeholder,
.ForRentHE .form-group textarea::placeholder {
    color: #9ca3af;
    opacity: 1;
}

.ForRentHE .submit-message {
    margin-top: 1.5rem;
    padding: 16px 24px;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    font-size: 1rem;
    animation: slideInUp 0.3s ease-out;
}

.ForRentHE .submit-message.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.ForRentHE .submit-message.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Privacy Checkbox */
.ForRentHE .privacy-checkbox-group {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.ForRentHE .privacy-checkbox-group:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.ForRentHE .privacy-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.5;
    color: #2c3e50;
    text-align: right;
    direction: rtl;
}

.ForRentHE .privacy-checkbox {
    display: none;
}

.ForRentHE .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #667eea;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
    order: 2;
}

.ForRentHE .privacy-checkbox:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.ForRentHE .privacy-checkbox:checked + .checkmark::after {
    content: "✓";
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.ForRentHE .privacy-text {
    flex: 1;
    order: 1;
}

.ForRentHE .privacy-text a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.ForRentHE .privacy-text a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Form Button Container */
.ForRentHE .form-button-container {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
}

/* Final CTA Section */
.ForRentHE .final-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.ForRentHE .final-cta-section h2 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.ForRentHE .final-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ForRentHE .container {
        padding: 0 15px;
    }

    .ForRentHE .hero-section {
        padding: 60px 0;
        min-height: 70vh;
        flex-direction: column;
        text-align: center;
    }

    .ForRentHE .hero-content h1 {
        font-size: 2.5rem;
    }

    .ForRentHE .hero-subtitle {
        font-size: 1.1rem;
    }

    .ForRentHE .hero-image {
        margin-top: 2rem;
        max-width: 100%;
    }

    .ForRentHE .services-section,
    .ForRentHE .why-choose-section,
    .ForRentHE .testimonials-section,
    .ForRentHE .contact-section,
    .ForRentHE .final-cta-section {
        padding: 60px 0;
    }

    .ForRentHE .services-section h2,
    .ForRentHE .why-choose-section h2,
    .ForRentHE .testimonials-section h2,
    .ForRentHE .contact-section h2,
    .ForRentHE .final-cta-section h2 {
        font-size: 2.2rem;
    }

    .ForRentHE .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .ForRentHE .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .ForRentHE .hero-content h1 {
        font-size: 2rem;
    }

    .ForRentHE .hero-subtitle {
        font-size: 1rem;
    }

    .ForRentHE .services-section h2,
    .ForRentHE .why-choose-section h2,
    .ForRentHE .testimonials-section h2,
    .ForRentHE .contact-section h2,
    .ForRentHE .final-cta-section h2 {
        font-size: 1.8rem;
    }

    .ForRentHE .service-card,
    .ForRentHE .testimonial-card {
        padding: 1.5rem;
    }

    .ForRentHE .feature-item {
        padding: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ForRentHE .service-card,
.ForRentHE .feature-item,
.ForRentHE .testimonial-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Modern Form Styling */
.ForRentHE .contact-form {
    max-width: 700px;
    margin: 0 auto;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.ForRentHE .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.ForRentHE .form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.ForRentHE .form-group label {
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.ForRentHE .form-group input,
.ForRentHE .form-group textarea {
    padding: 16px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    direction: ltr;
}

.ForRentHE .form-group input:focus,
.ForRentHE .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.ForRentHE .form-group textarea {
    resize: vertical;
    min-height: 140px;
    font-family: inherit;
    direction: rtl;
}

.ForRentHE .form-group input::placeholder,
.ForRentHE .form-group textarea::placeholder {
    color: #9ca3af;
    opacity: 1;
}

/* Privacy Checkbox */
.ForRentHE .privacy-checkbox-group {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.ForRentHE .privacy-checkbox-group:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.ForRentHE .privacy-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.5;
    color: #2c3e50;
    direction: rtl;
}

.ForRentHE .privacy-checkbox {
    display: none;
}

.ForRentHE .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #667eea;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.ForRentHE .privacy-checkbox:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.ForRentHE .privacy-checkbox:checked + .checkmark::after {
    content: "✓";
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.ForRentHE .privacy-text {
    flex: 1;
}

.ForRentHE .privacy-text a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.ForRentHE .privacy-text a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Submit Message */
.ForRentHE .submit-message {
    margin-top: 1.5rem;
    padding: 16px 24px;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    font-size: 1rem;
    animation: slideInUp 0.3s ease-out;
}

.ForRentHE .submit-message.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.ForRentHE .submit-message.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

/* Form Button Container */
.ForRentHE .form-button-container {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
}

/* Advanced Animation Enhancements */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Apply animations to elements */
.ForRentHE .feature-item {
    animation: slideInRight 0.8s ease-out;
}

.ForRentHE .feature-item:nth-child(even) {
    animation-name: slideInLeft;
}

.ForRentHE .testimonial-card {
    animation: scaleIn 0.8s ease-out;
}

.ForRentHE .testimonial-card:nth-child(1) {
    animation-delay: 0.1s;
}

.ForRentHE .testimonial-card:nth-child(2) {
    animation-delay: 0.2s;
}

.ForRentHE .testimonial-card:nth-child(3) {
    animation-delay: 0.3s;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ForRentHE .container {
        padding: 0 15px;
    }

    .ForRentHE .hero-section {
        padding: 60px 0;
        min-height: 70vh;
        flex-direction: column;
        text-align: center;
    }

    .ForRentHE .hero-content h1 {
        font-size: 2.5rem;
    }

    .ForRentHE .hero-subtitle {
        font-size: 1.1rem;
    }

    .ForRentHE .hero-image {
        margin-top: 2rem;
        max-width: 100%;
    }

    .ForRentHE .services-section,
    .ForRentHE .why-choose-section,
    .ForRentHE .testimonials-section,
    .ForRentHE .contact-section,
    .ForRentHE .final-cta-section {
        padding: 60px 0;
    }

    .ForRentHE .services-section h2,
    .ForRentHE .why-choose-section h2,
    .ForRentHE .testimonials-section h2,
    .ForRentHE .contact-section h2,
    .ForRentHE .final-cta-section h2 {
        font-size: 2.2rem;
    }

    .ForRentHE .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .ForRentHE .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .ForRentHE .hero-content h1 {
        font-size: 2rem;
    }

    .ForRentHE .hero-subtitle {
        font-size: 1rem;
    }

    .ForRentHE .services-section h2,
    .ForRentHE .why-choose-section h2,
    .ForRentHE .testimonials-section h2,
    .ForRentHE .contact-section h2,
    .ForRentHE .final-cta-section h2 {
        font-size: 1.8rem;
    }

    .ForRentHE .service-card,
    .ForRentHE .testimonial-card {
        padding: 1.5rem;
    }

    .ForRentHE .feature-item {
        padding: 1.5rem;
    }
}

/* Accessibility */
.ForRentHE .cta-button:focus,
.ForRentHE .form-group input:focus,
.ForRentHE .form-group textarea:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .ForRentHE .hero-section,
    .ForRentHE .final-cta-section {
        background: white !important;
        color: black !important;
    }

    .ForRentHE .cta-button {
        display: none;
    }
}

/* Print Styles */
@media print {
    .ForRentHE .hero-section,
    .ForRentHE .final-cta-section {
        background: white !important;
        color: black !important;
    }

    .ForRentHE .cta-button {
        display: none;
    }
}
