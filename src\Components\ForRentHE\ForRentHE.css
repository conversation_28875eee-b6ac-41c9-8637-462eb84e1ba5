/* ForRentHE Component Styles - Hebrew RTL */
.ForRentHE {
    width: 100%;
    min-height: 100vh;
    direction: rtl;
    font-family: 'Poppins', 'Arial', sans-serif;
}

/* Container */
.ForRentHE .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.ForRentHE .hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    min-height: 80vh;
    direction: rtl;
}

.ForRentHE .hero-content {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.ForRentHE .hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.ForRentHE .hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.9;
}

.ForRentHE .hero-image {
    flex: 1;
    max-width: 600px;
    margin: 2rem auto 0;
}

.ForRentHE .hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Buttons */
.ForRentHE .cta-button {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.ForRentHE .cta-button.primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.ForRentHE .cta-button.primary:hover {
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
    transform: translateY(-2px);
}

.ForRentHE .cta-button.secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.ForRentHE .cta-button.secondary:hover {
    background: #667eea;
    color: white;
}

.ForRentHE .cta-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Services Section */
.ForRentHE .services-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForRentHE .services-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForRentHE .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForRentHE .service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ForRentHE .service-card:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.ForRentHE .service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.ForRentHE .service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForRentHE .service-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Why Choose Section */
.ForRentHE .why-choose-section {
    padding: 100px 0;
    background: white;
}

.ForRentHE .why-choose-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForRentHE .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForRentHE .feature-item {
    padding: 2rem;
    border-right: 4px solid #667eea;
    border-left: none;
    background: #f8f9fa;
    border-radius: 10px;
}

.ForRentHE .feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForRentHE .feature-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* Testimonials Section */
.ForRentHE .testimonials-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForRentHE .testimonials-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForRentHE .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForRentHE .testimonial-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ForRentHE .testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.ForRentHE .testimonial-author strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.ForRentHE .testimonial-author span {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Contact Section */
.ForRentHE .contact-section {
    padding: 100px 0;
    background: white;
}

.ForRentHE .contact-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForRentHE .section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.ForRentHE .contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.ForRentHE .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.ForRentHE .form-group {
    display: flex;
    flex-direction: column;
}

.ForRentHE .form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
}

.ForRentHE .form-group input,
.ForRentHE .form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    text-align: right;
    direction: rtl;
}

.ForRentHE .form-group input:focus,
.ForRentHE .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.ForRentHE .form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.ForRentHE .submit-message {
    margin-top: 1rem;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
}

.ForRentHE .submit-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ForRentHE .submit-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Final CTA Section */
.ForRentHE .final-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.ForRentHE .final-cta-section h2 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.ForRentHE .final-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ForRentHE .container {
        padding: 0 15px;
    }

    .ForRentHE .hero-section {
        padding: 60px 0;
        min-height: 70vh;
        flex-direction: column;
        text-align: center;
    }

    .ForRentHE .hero-content h1 {
        font-size: 2.5rem;
    }

    .ForRentHE .hero-subtitle {
        font-size: 1.1rem;
    }

    .ForRentHE .hero-image {
        margin-top: 2rem;
        max-width: 100%;
    }

    .ForRentHE .services-section,
    .ForRentHE .why-choose-section,
    .ForRentHE .testimonials-section,
    .ForRentHE .contact-section,
    .ForRentHE .final-cta-section {
        padding: 60px 0;
    }

    .ForRentHE .services-section h2,
    .ForRentHE .why-choose-section h2,
    .ForRentHE .testimonials-section h2,
    .ForRentHE .contact-section h2,
    .ForRentHE .final-cta-section h2 {
        font-size: 2.2rem;
    }

    .ForRentHE .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForRentHE .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .ForRentHE .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .ForRentHE .hero-content h1 {
        font-size: 2rem;
    }

    .ForRentHE .hero-subtitle {
        font-size: 1rem;
    }

    .ForRentHE .services-section h2,
    .ForRentHE .why-choose-section h2,
    .ForRentHE .testimonials-section h2,
    .ForRentHE .contact-section h2,
    .ForRentHE .final-cta-section h2 {
        font-size: 1.8rem;
    }

    .ForRentHE .service-card,
    .ForRentHE .testimonial-card {
        padding: 1.5rem;
    }

    .ForRentHE .feature-item {
        padding: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ForRentHE .service-card,
.ForRentHE .feature-item,
.ForRentHE .testimonial-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
.ForRentHE .cta-button:focus,
.ForRentHE .form-group input:focus,
.ForRentHE .form-group textarea:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .ForRentHE .hero-section,
    .ForRentHE .final-cta-section {
        background: white !important;
        color: black !important;
    }

    .ForRentHE .cta-button {
        display: none;
    }
}
