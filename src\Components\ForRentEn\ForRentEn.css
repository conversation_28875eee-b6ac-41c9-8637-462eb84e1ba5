/* ForRentEn Component Styles */
.ForRentEn {
    width: 100%;
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    min-height: 80vh;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.9;
}

.hero-image {
    flex: 1;
    max-width: 600px;
    margin: 2rem auto 0;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Buttons */
.cta-button {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.cta-button.primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.cta-button.primary:hover {
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
    transform: translateY(-2px);
}

.cta-button.secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.cta-button.secondary:hover {
    background: #667eea;
    color: white;
}

.cta-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Services Section */
.services-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.services-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.service-card:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.service-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Why Choose Section */
.why-choose-section {
    padding: 100px 0;
    background: white;
}

.why-choose-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    padding: 2rem;
    border-left: 4px solid #667eea;
    background: #f8f9fa;
    border-radius: 10px;
}

.feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.feature-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.testimonials-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.testimonial-author strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.testimonial-author span {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Contact Section */
.contact-section {
    padding: 100px 0;
    background: white;
}

.contact-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-message {
    margin-top: 1rem;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
}

.submit-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.submit-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Final CTA Section */
.final-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.final-cta-section h2 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.final-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .hero-section {
        padding: 60px 0;
        min-height: 70vh;
        flex-direction: column;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-image {
        margin-top: 2rem;
        max-width: 100%;
    }

    .services-section,
    .why-choose-section,
    .testimonials-section,
    .contact-section,
    .final-cta-section {
        padding: 60px 0;
    }

    .services-section h2,
    .why-choose-section h2,
    .testimonials-section h2,
    .contact-section h2,
    .final-cta-section h2 {
        font-size: 2.2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .services-section h2,
    .why-choose-section h2,
    .testimonials-section h2,
    .contact-section h2,
    .final-cta-section h2 {
        font-size: 1.8rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1.5rem;
    }

    .feature-item {
        padding: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.service-card,
.feature-item,
.testimonial-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
.cta-button:focus,
.form-group input:focus,
.form-group textarea:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .hero-section,
    .final-cta-section {
        background: white !important;
        color: black !important;
    }

    .cta-button {
        display: none;
    }
}
