import type { JSX } from "react";
import { motion } from "framer-motion";
import "./Button.css";

interface ButtonProps {
    children: React.ReactNode;
    variant?: 'primary' | 'secondary';
    size?: 'small' | 'medium' | 'large';
    onClick?: () => void;
    disabled?: boolean;
    type?: 'button' | 'submit' | 'reset';
    className?: string;
}

export function Button({
    children,
    variant = 'primary',
    size = 'medium',
    onClick,
    disabled = false,
    type = 'button',
    className = ''
}: ButtonProps): JSX.Element {
    const buttonClass = `btn btn-${variant} btn-${size} ${className}`;

    return (
        <motion.button
            className={buttonClass}
            type={type}
            onClick={onClick}
            disabled={disabled}
            whileHover={{ scale: disabled ? 1 : 1.05 }}
            whileTap={{ scale: disabled ? 1 : 0.95 }}
            transition={{ duration: 0.2 }}
        >
            {children}
        </motion.button>
    );
}
