import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './ImageSlideshow.css';

interface ImageSlideshowProps {
    className?: string;
    alt?: string;
    autoPlayInterval?: number;
    showDots?: boolean;
    showArrows?: boolean;
    height?: string;
}

export function ImageSlideshow({
    className = '',
    alt = 'Property image',
    autoPlayInterval = 4000,
    showDots = false,
    showArrows = false,
    height
}: ImageSlideshowProps) {
    const [images, setImages] = useState<string[]>([]);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    // Dynamically import all images from assets folder
    useEffect(() => {
        const loadImages = async () => {
            try {
                // Get all image files from assets folder
                const imageModules = import.meta.glob('/src/assets/*.{png,jpg,jpeg,webp,gif}');
                const imagePromises = Object.entries(imageModules).map(async ([, importFn]) => {
                    const module = await importFn() as { default: string };
                    return module.default;
                });
                
                const loadedImages = await Promise.all(imagePromises);
                
                if (loadedImages.length > 0) {
                    setImages(loadedImages);
                } else {
                    // Fallback to placeholder if no images found
                    setImages(['/api/placeholder/600/400']);
                }
            } catch (error) {
                // Fallback to placeholder on error
                setImages(['/api/placeholder/600/400']);
            } finally {
                setIsLoading(false);
            }
        };

        loadImages();
    }, []);

    // Auto-play functionality
    useEffect(() => {
        if (images.length <= 1) return;

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) => 
                prevIndex === images.length - 1 ? 0 : prevIndex + 1
            );
        }, autoPlayInterval);

        return () => clearInterval(interval);
    }, [images.length, autoPlayInterval]);

    const goToSlide = (index: number) => {
        setCurrentIndex(index);
    };

    const goToPrevious = () => {
        setCurrentIndex(currentIndex === 0 ? images.length - 1 : currentIndex - 1);
    };

    const goToNext = () => {
        setCurrentIndex(currentIndex === images.length - 1 ? 0 : currentIndex + 1);
    };

    if (isLoading) {
        return (
            <div className={`image-slideshow loading ${className}`}>
                <div className="loading-placeholder">
                    <div className="loading-spinner"></div>
                </div>
            </div>
        );
    }

    const containerStyle = height ? { height } : {};

    return (
        <div className={`image-slideshow ${className}`} style={containerStyle}>
            <div className="slideshow-container">
                <AnimatePresence mode="wait">
                    <motion.div
                        key={currentIndex}
                        className="slideshow-image"
                        role="img"
                        aria-label={`${alt} ${currentIndex + 1}`}
                        style={{
                            backgroundImage: `url(${images[currentIndex]})`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center center',
                            backgroundRepeat: 'no-repeat',
                            width: '100%',
                            height: '100%',
                            borderRadius: '15px'
                        }}
                        initial={{ opacity: 0, scale: 1.1 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{
                            duration: 0.8,
                            ease: [0.4, 0, 0.2, 1]
                        }}
                    />
                </AnimatePresence>

                {/* Navigation Arrows */}
                {showArrows && images.length > 1 && (
                    <>
                        <motion.button
                            className="slideshow-arrow prev"
                            onClick={goToPrevious}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                        >
                            ‹
                        </motion.button>
                        <motion.button
                            className="slideshow-arrow next"
                            onClick={goToNext}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                        >
                            ›
                        </motion.button>
                    </>
                )}
            </div>

            {/* Dots Indicator */}
            {showDots && images.length > 1 && (
                <div className="slideshow-dots">
                    {images.map((_, index) => (
                        <motion.button
                            key={index}
                            className={`dot ${index === currentIndex ? 'active' : ''}`}
                            onClick={() => goToSlide(index)}
                            whileHover={{ scale: 1.2 }}
                            whileTap={{ scale: 0.8 }}
                        />
                    ))}
                </div>
            )}


        </div>
    );
}
