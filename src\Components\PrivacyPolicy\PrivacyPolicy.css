/* PrivacyPolicy Component Styles */
.PrivacyPolicy {
    width: 100%;
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.privacy-hero {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.privacy-hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Content Section */
.privacy-content {
    padding: 80px 0;
    background: #f8f9fa;
}

.policy-section {
    background: white;
    padding: 3rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.policy-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.policy-section h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-weight: 600;
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
}

.policy-section p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #4a5568;
    margin-bottom: 1.5rem;
}

.policy-section ul {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.policy-section li {
    font-size: 1rem;
    line-height: 1.6;
    color: #4a5568;
    margin-bottom: 1rem;
    padding-left: 2rem;
    position: relative;
}

.policy-section li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: #667eea;
    font-weight: bold;
    font-size: 1.2rem;
}

.policy-section strong {
    color: #2c3e50;
    font-weight: 600;
}

/* Consent Box */
.consent-box {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px solid #667eea;
    border-radius: 12px;
    padding: 2rem;
    margin: 1.5rem 0;
}

.consent-box p {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.consent-box ul {
    margin-top: 1rem;
}

.consent-box li::before {
    content: "⚠️";
    font-size: 1rem;
}

/* Contact Section */
.PrivacyPolicy .contact-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.PrivacyPolicy .contact-section h2 {
    color: white;
    border-bottom-color: white;
}

.contact-section p {
    color: white;
    opacity: 0.9;
}

.contact-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    backdrop-filter: blur(10px);
}

.contact-info p {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .privacy-hero {
        padding: 80px 0 60px;
    }

    .privacy-hero h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .privacy-content {
        padding: 60px 0;
    }

    .policy-section {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }

    .policy-section h2 {
        font-size: 1.6rem;
    }

    .policy-section p {
        font-size: 1rem;
    }

    .policy-section li {
        font-size: 0.9rem;
        padding-left: 1.5rem;
    }

    .consent-box {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .privacy-hero h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .policy-section {
        padding: 1.5rem;
    }

    .policy-section h2 {
        font-size: 1.4rem;
    }

    .consent-box {
        padding: 1rem;
    }
}

/* Animation Enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.policy-section {
    animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
.policy-section:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .privacy-hero {
        background: white !important;
        color: black !important;
    }
    
    .contact-section {
        background: white !important;
        color: black !important;
    }
    
    .policy-section {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
