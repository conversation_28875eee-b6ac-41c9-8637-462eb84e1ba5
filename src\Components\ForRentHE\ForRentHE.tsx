import type { JSX } from "react";
import { useState } from "react";
import { motion } from "framer-motion";
import { SEO } from "../SEO/SEO";

import { LeadUser } from "../../models/LeadUser";
import leadService from "../../services/LeadService";
import "./ForRentHE.css";

export function ForRentHE(): JSX.Element {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        message: ""
    });
    const [privacyAccepted, setPrivacyAccepted] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!privacyAccepted) {
            setSubmitMessage("אנא אשר את מדיניות הפרטיות כדי להמשיך.");
            return;
        }

        setIsSubmitting(true);
        setSubmitMessage("");

        try {
            const leadUser = new LeadUser(
                0, // id will be set by backend
                formData.firstName,
                formData.lastName,
                formData.email,
                formData.phoneNumber,
                formData.message,
                "for rent hebrew"
            );

            await leadService.addLead(leadUser);
            setSubmitMessage("תודה! ניצור איתך קשר בקרוב.");
            setFormData({
                firstName: "",
                lastName: "",
                email: "",
                phoneNumber: "",
                message: ""
            });
            setPrivacyAccepted(false);
        } catch (error) {
            setSubmitMessage("משהו השתבש. אנא נסה שוב.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <SEO
                title="דירות יוקרה להשכרה בישראל | נכסי פרימיום תל אביב, ירושלים, חיפה | מתווך דו לשוני"
                description="גלה דירות יוקרה, פנטהאוזים, וילות ובתים להשכרה בישראל. שירותי נדלן דו-לשוניים מקצועיים בתל אביב, ירושלים, חיפה, הרצליה, נתניה. נכסים בלעדיים, דירות מרוהטות, השכרה קצרת וארוכת טווח. ניהול נכסים מקצועי וסיוע בהעתקה."
                keywords="השכרת דירות ישראל, דירות יוקרה תל אביב, בתים להשכרה ירושלים, נכסים להשכרה חיפה, נדלן ישראל השכרה, מתווך דו לשוני, דירות מרוהטות תל אביב, פנטהאוז להשכרה ירושלים, וילה להשכרה הרצליה, השכרה קצרת טווח ישראל, השכרה ארוכת טווח תל אביב, ניהול נכסים ישראל, שירותי העתקה ישראל, דיור עולים ישראל, דיור יוקרה תל אביב, השכרות פרימיום ירושלים, נכסים בלעדיים חיפה, מתווך נדלן ישראל, חיפוש דירה תל אביב, השכרת בית ירושלים, השכרת נכס נתניה, נדלן יוקרה ישראל, נדלן בינלאומי ישראל, מתווך דובר אנגלית ישראל, נדלן עברית אנגלית, חיפוש נכס ישראל, סיוע השכרה ישראל, פתרונות דיור ישראל, השכרת נכס פרימיום, דיור בכירים ישראל, דיור תאגידי תל אביב"
                ogTitle="דירות יוקרה להשכרה בישראל - שירותי נדלן דו-לשוניים מקצועיים"
                ogDescription="מצא את נכס החלומות שלך להשכרה בישראל עם הצוות הדו-לשוני המקצועי שלנו. דירות יוקרה, פנטהאוזים, וילות ונכסים בלעדיים ברחבי תל אביב, ירושלים, חיפה ועוד. שירותי העתקה וניהול נכסים מקצועיים."
                ogImage="/rental-hero-he.jpg"
                ogUrl="https://yourdomain.com/for-rent-he"
            />
            <div className="ForRentHE" dir="rtl">
                {/* Hero Section */}
                <motion.section
                    className="hero-section"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <div className="hero-content">
                        <h1>מצא את בית החלומות שלך להשכרה בישראל</h1>
                        <p className="hero-subtitle">
                            גלה דירות יוקרה ובתים ברחבי תל אביב, ירושלים, חיפה ועוד.
                            שירות דו-לשוני מקצועי עם גישה בלעדית לנכסי השכרה מובחרים.
                        </p>
                        <motion.button
                            className="cta-button primary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            התחל את החיפוש היום
                        </motion.button>
                    </div>
                    <div className="hero-image">
                        <img src="/api/placeholder/600/400" alt="נכס יוקרה להשכרה בישראל" />
                    </div>
                </motion.section>

                {/* Services Section */}
                <motion.section
                    className="services-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <motion.h2
                            initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            שירותי השכרה שלנו
                        </motion.h2>
                        <div className="services-grid">
                            <motion.div
                                className="service-card"
                                initial={{ opacity: 0, y: 80 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -15, scale: 1.03 }}
                            >
                                <div className="service-icon">🏠</div>
                                <h3>דירות יוקרה</h3>
                                <p>דירות מובחרות במיקומים מרכזיים ברחבי הערים הגדולות בישראל עם אמצעי נוחות מודרניים ונוף מרהיב.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                initial={{ opacity: 0, y: 80 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -15, scale: 1.03 }}
                            >
                                <div className="service-icon">🏡</div>
                                <h3>בתים משפחתיים</h3>
                                <p>בתים מרווחים עם גינות, מושלמים למשפחות המחפשות נוחות ופרטיות בשכונות מגורים שקטות.</p>
                            </motion.div>
                            <motion.div
                                className="service-card"
                                initial={{ opacity: 0, y: 80 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                                viewport={{ once: true }}
                                whileHover={{ y: -15, scale: 1.03 }}
                            >
                                <div className="service-icon">🌟</div>
                                <h3>נכסים בלעדיים</h3>
                                <p>גישה לנכסים שלא מוצעים בשוק הפתוח והזדמנויות השכרה בלעדיות שלא זמינות בערוצים אחרים.</p>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Why Choose Us Section */}
                <motion.section
                    className="why-choose-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>למה לבחור בשירותי השכרה שלנו?</h2>
                        <div className="features-grid">
                            <div className="feature-item">
                                <h3>🌍 מומחיות דו-לשונית</h3>
                                <p>הצוות שלנו דובר עברית ואנגלית ברמה גבוהה, מה שמבטיח תקשורת חלקה לאורך כל תהליך השכרה.</p>
                            </div>
                            <div className="feature-item">
                                <h3>📍 מיקומים מובחרים</h3>
                                <p>התמחות בשכונות הנחשקות ביותר בתל אביב, ירושלים, חיפה, נתניה ועוד ערים מרכזיות בישראל.</p>
                            </div>
                            <div className="feature-item">
                                <h3>⚡ מענה מהיר</h3>
                                <p>התאמת נכסים מהירה וארגון צפיות. אנחנו מבינים את השוק התחרותי של השכרות בישראל.</p>
                            </div>
                            <div className="feature-item">
                                <h3>🤝 שירות אישי</h3>
                                <p>ליווי מסור מחיפוש ראשוני ועד חתימת חוזה, כולל סיוע בתיעוד ודרישות משפטיות.</p>
                            </div>
                        </div>
                    </div>
                </motion.section>

                {/* Testimonials Section */}
                <motion.section
                    className="testimonials-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>מה הלקוחות שלנו אומרים</h2>
                        <div className="testimonials-grid">
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"מצאתי את הדירה המושלמת בתל אביב תוך שבוע! השירות הדו-לשוני הפך הכל להרבה יותר קל בתור עולה חדשה."</p>
                                    <div className="testimonial-author">
                                        <strong>שרה מ.</strong>
                                        <span>עולה חדשה מארה"ב</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"שירות יוצא דופן! עזרו לנו למצוא בית משפחתי יפהפה בירושלים עם גינה לילדים שלנו."</p>
                                    <div className="testimonial-author">
                                        <strong>דוד ורחל כ.</strong>
                                        <span>משפחה מקנדה</span>
                                    </div>
                                </div>
                            </motion.div>
                            <motion.div
                                className="testimonial-card"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="testimonial-content">
                                    <p>"מקצועיים, מגיבים מהר ובאמת הבינו את הצרכים שלנו. ממליץ בחום לכל מי שמחפש להשכיר בישראל!"</p>
                                    <div className="testimonial-author">
                                        <strong>מיכאל ט.</strong>
                                        <span>איש עסקים</span>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </motion.section>

                {/* Contact Form Section */}
                <motion.section
                    id="contact-form"
                    className="contact-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>התחל את חיפוש השכרה שלך היום</h2>
                        <p className="section-subtitle">ספר לנו על נכס השכרה האידיאלי שלך ואנחנו נמצא עבורך את ההתאמה המושלמת.</p>

                        <form onSubmit={handleSubmit} className="contact-form">
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">שם פרטי *</label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        value={formData.firstName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">שם משפחה *</label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        value={formData.lastName}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="email">אימייל *</label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="phoneNumber">מספר טלפון *</label>
                                    <input
                                        type="tel"
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        value={formData.phoneNumber}
                                        onChange={handleInputChange}
                                        placeholder="50-123-4567"
                                        required
                                        dir="ltr"
                                    />
                                </div>
                            </div>
                            <div className="form-group">
                                <label htmlFor="message">ספר לנו על נכס השכרה האידיאלי שלך</label>
                                <textarea
                                    id="message"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    rows={4}
                                    placeholder="העדפות מיקום, מספר חדרים, טווח תקציב, תאריך כניסה וכו'"
                                />
                            </div>

                            <div className="privacy-checkbox-group">
                                <label className="privacy-checkbox-label">
                                    <input
                                        type="checkbox"
                                        checked={privacyAccepted}
                                        onChange={(e) => setPrivacyAccepted(e.target.checked)}
                                        required
                                        className="privacy-checkbox"
                                    />
                                    <span className="checkmark"></span>
                                    <span className="privacy-text">
                                        אני מסכים ל<a href="/privacy-policy" target="_blank" rel="noopener noreferrer">מדיניות הפרטיות</a> ומסכים
                                        להיות מוזמן ליצירת קשר באמצעות טלפון, אימייל או הודעת טקסט בנוגע להזדמנויות נדלן.
                                    </span>
                                </label>
                            </div>
                            <div className="form-button-container">
                                <motion.button
                                    type="submit"
                                    className="cta-button primary"
                                    disabled={isSubmitting}
                                    whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
                                    whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                                >
                                    {isSubmitting ? "שולח..." : "מצא לי את השכרה המושלמת"}
                                </motion.button>
                                {submitMessage && (
                                    <div className={`submit-message ${submitMessage.includes('תודה') ? 'success' : 'error'}`}>
                                        {submitMessage}
                                    </div>
                                )}
                            </div>
                        </form>
                    </div>
                </motion.section>

                {/* Final CTA Section */}
                <motion.section
                    className="final-cta-section"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="container">
                        <h2>מוכן למצוא את דירת החלומות שלך להשכרה בישראל?</h2>
                        <p>הצטרף למאות לקוחות מרוצים שמצאו את הבית המושלם שלהם דרך שירותי השכרה המקצועיים שלנו.</p>
                        <motion.button
                            className="cta-button secondary"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                        >
                            צור קשר היום
                        </motion.button>
                    </div>
                </motion.section>
            </div>
        </>
    );
}
