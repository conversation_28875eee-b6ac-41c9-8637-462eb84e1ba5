import type { JSX } from "react";
import { Routes, Route } from "react-router-dom";
import { ForRentEn } from "../ForRentEn/ForRentEn";
import { ForSaleEn } from "../ForSaleEn/ForSaleEn";
import { ForRentHE } from "../ForRentHE/ForRentHE";
import { ForSaleHE } from "../ForSaleHE/ForSaleHE";

export function Routing(): JSX.Element {
    return (
        <div className="Routing">
            <Routes>
                <Route path="/for-rent-en" element={<ForRentEn />} />
                <Route path="/for-sale-en" element={<ForSaleEn />} />
                <Route path="/for-rent-he" element={<ForRentHE />} />
                <Route path="/for-sale-he" element={<ForSaleHE />} />
            </Routes>
        </div>
    );
}
