/* Shared Hero Section Styles */
.hero-section {
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-container {
    display: flex;
    align-items: center;
    gap: 4rem;
    width: 100%;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.9;
}

.hero-image {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
}

/* Hero Backgrounds */
.hero-light {
    background: #ffffff;
    color: #333333;
}

.hero-dark {
    background: #1a1a1a;
    color: #ffffff;
}

.hero-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
        min-height: 70vh;
    }
    
    .hero-container {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-image {
        margin-top: 0;
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-image {
        margin-top: 0;
        max-width: 100%;
    }
}
