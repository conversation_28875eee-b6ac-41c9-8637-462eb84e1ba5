/* Home Component Styles */
.Home {
    width: 100%;
    min-height: 100vh;
    position: relative;
}

.Home.rtl {
    direction: rtl;
    font-family: 'Poppins', 'Arial', sans-serif;
}

.Home.ltr {
    direction: ltr;
    font-family: 'Poppins', sans-serif;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Language Switcher */
.language-switcher {
    position: fixed;
    top: 30px;
    right: 30px;
    z-index: 1000;
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50px;
    padding: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.Home.rtl .language-switcher {
    right: auto;
    left: 30px;
}

.lang-button {
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #6c757d;
}

.lang-button.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.lang-button:hover:not(.active) {
    background: #f8f9fa;
    color: #2c3e50;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 100px;
    display: flex;
    align-items: center;
    min-height: 90vh;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    line-height: 1.6;
    opacity: 0.95;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-image {
    flex: 1;
    max-width: 600px;
    margin: 2rem auto 0;
    position: relative;
    z-index: 2;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.hero-image img:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.02);
}

/* Buttons */
.cta-button {
    padding: 18px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 200px;
    position: relative;
    overflow: hidden;
    margin: 10px 0;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button.primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
    border: 2px solid transparent;
}

.cta-button.primary:hover {
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.5);
    transform: translateY(-3px) scale(1.02);
}

.cta-button.secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
    backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
    background: white;
    color: #667eea;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(255, 255, 255, 0.3);
}

/* Services Section */
.services-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.services-section h2 {
    text-align: center;
    font-size: 3.2rem;
    margin-bottom: 4rem;
    color: #2c3e50;
    font-weight: 700;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.service-card {
    background: white;
    padding: 3rem;
    border-radius: 25px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover::before {
    opacity: 1;
}

.service-card.sale {
    border-image: linear-gradient(135deg, #e74c3c, #c0392b) 1;
}

.service-card.rent {
    border-image: linear-gradient(135deg, #667eea, #764ba2) 1;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
}

.service-icon {
    font-size: 4rem;
    margin-bottom: 2rem;
    display: block;
}

.service-card h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
    font-weight: 600;
}

.service-card p {
    color: #6c757d;
    line-height: 1.7;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.service-arrow {
    font-size: 1.5rem;
    color: #667eea;
    font-weight: bold;
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease;
}

.Home.rtl .service-arrow {
    transform: translateX(20px);
}

.service-card:hover .service-arrow {
    opacity: 1;
    transform: translateX(0);
}

/* Why Choose Section */
.why-choose-section {
    padding: 120px 0;
    background: white;
}

.why-choose-section h2 {
    text-align: center;
    font-size: 3.2rem;
    margin-bottom: 4rem;
    color: #2c3e50;
    font-weight: 700;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.feature-item {
    text-align: center;
    padding: 2.5rem;
    border-radius: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e9ecef;
}

.feature-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
}

.feature-item h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.feature-item p {
    color: #6c757d;
    line-height: 1.6;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .language-switcher {
        top: 20px;
        right: 20px;
        padding: 6px;
    }

    .Home.rtl .language-switcher {
        right: auto;
        left: 20px;
    }

    .lang-button {
        padding: 10px 16px;
        font-size: 0.8rem;
    }

    .hero-section {
        padding: 100px 0 80px;
        min-height: 80vh;
        flex-direction: column;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.8rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-image {
        margin-top: 3rem;
        max-width: 100%;
    }

    .hero-image img {
        transform: none;
        border-radius: 15px;
    }

    .services-section,
    .why-choose-section {
        padding: 80px 0;
    }

    .services-section h2,
    .why-choose-section h2 {
        font-size: 2.5rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-card {
        padding: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature-item {
        padding: 2rem;
    }

    .cta-button {
        padding: 16px 32px;
        font-size: 1rem;
        min-width: 180px;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .services-section h2,
    .why-choose-section h2 {
        font-size: 2rem;
    }

    .service-card,
    .feature-item {
        padding: 1.5rem;
    }

    .service-icon,
    .feature-icon {
        font-size: 2.5rem;
    }
}

/* Advanced Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.service-icon {
    animation: float 6s ease-in-out infinite;
}

.feature-icon {
    animation: pulse 4s ease-in-out infinite;
}

.hero-image img {
    animation: float 8s ease-in-out infinite;
}

/* Scroll Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Accessibility */
.cta-button:focus,
.lang-button:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .hero-section,
    .language-switcher {
        display: none;
    }

    .services-section,
    .why-choose-section {
        background: white !important;
        color: black !important;
    }
}
