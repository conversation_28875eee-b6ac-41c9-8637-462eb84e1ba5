/* ForSaleHE Component Styles - Hebrew RTL */
.ForSaleHE {
    width: 100%;
    min-height: 100vh;
    direction: rtl;
    font-family: 'Poppins', 'Arial', sans-serif;
}

/* Container */
.ForSaleHE .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.ForSaleHE .hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    min-height: 80vh;
    direction: rtl;
}

.ForSaleHE .hero-content {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.ForSaleHE .hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.ForSaleHE .hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.9;
}

.ForSaleHE .hero-image {
    flex: 1;
    max-width: 600px;
    margin: 2rem auto 0;
}

.ForSaleHE .hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Buttons */
.ForSaleHE .cta-button {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.ForSaleHE .cta-button.primary {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.ForSaleHE .cta-button.primary:hover {
    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.4);
    transform: translateY(-2px);
}

.ForSaleHE .cta-button.secondary {
    background: white;
    color: #2c3e50;
    border: 2px solid #2c3e50;
}

.ForSaleHE .cta-button.secondary:hover {
    background: #2c3e50;
    color: white;
}

.ForSaleHE .cta-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Services Section */
.ForSaleHE .services-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForSaleHE .services-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForSaleHE .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForSaleHE .service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ForSaleHE .service-card:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.ForSaleHE .service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.ForSaleHE .service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForSaleHE .service-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Why Choose Section */
.ForSaleHE .why-choose-section {
    padding: 100px 0;
    background: white;
}

.ForSaleHE .why-choose-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForSaleHE .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForSaleHE .feature-item {
    padding: 2rem;
    border-right: 4px solid #e74c3c;
    border-left: none;
    background: #f8f9fa;
    border-radius: 10px;
}

.ForSaleHE .feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForSaleHE .feature-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* Testimonials Section */
.ForSaleHE .testimonials-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForSaleHE .testimonials-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForSaleHE .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForSaleHE .testimonial-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ForSaleHE .testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.ForSaleHE .testimonial-author strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.ForSaleHE .testimonial-author span {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Contact Section */
.ForSaleHE .contact-section {
    padding: 100px 0;
    background: white;
}

.ForSaleHE .contact-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForSaleHE .section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.ForSaleHE .contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.ForSaleHE .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.ForSaleHE .form-group {
    display: flex;
    flex-direction: column;
}

.ForSaleHE .form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
}

.ForSaleHE .form-group input,
.ForSaleHE .form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    text-align: right;
    direction: rtl;
}

.ForSaleHE .form-group input:focus,
.ForSaleHE .form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

.ForSaleHE .form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.ForSaleHE .submit-message {
    margin-top: 1rem;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
}

.ForSaleHE .submit-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ForSaleHE .submit-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Final CTA Section */
.ForSaleHE .final-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    text-align: center;
}

.ForSaleHE .final-cta-section h2 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.ForSaleHE .final-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ForSaleHE .container {
        padding: 0 15px;
    }

    .ForSaleHE .hero-section {
        padding: 60px 0;
        min-height: 70vh;
        flex-direction: column;
        text-align: center;
    }

    .ForSaleHE .hero-content h1 {
        font-size: 2.5rem;
    }

    .ForSaleHE .hero-subtitle {
        font-size: 1.1rem;
    }

    .ForSaleHE .hero-image {
        margin-top: 2rem;
        max-width: 100%;
    }

    .ForSaleHE .services-section,
    .ForSaleHE .why-choose-section,
    .ForSaleHE .testimonials-section,
    .ForSaleHE .contact-section,
    .ForSaleHE .final-cta-section {
        padding: 60px 0;
    }

    .ForSaleHE .services-section h2,
    .ForSaleHE .why-choose-section h2,
    .ForSaleHE .testimonials-section h2,
    .ForSaleHE .contact-section h2,
    .ForSaleHE .final-cta-section h2 {
        font-size: 2.2rem;
    }

    .ForSaleHE .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForSaleHE .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForSaleHE .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForSaleHE .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .ForSaleHE .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .ForSaleHE .hero-content h1 {
        font-size: 2rem;
    }

    .ForSaleHE .hero-subtitle {
        font-size: 1rem;
    }

    .ForSaleHE .services-section h2,
    .ForSaleHE .why-choose-section h2,
    .ForSaleHE .testimonials-section h2,
    .ForSaleHE .contact-section h2,
    .ForSaleHE .final-cta-section h2 {
        font-size: 1.8rem;
    }

    .ForSaleHE .service-card,
    .ForSaleHE .testimonial-card {
        padding: 1.5rem;
    }

    .ForSaleHE .feature-item {
        padding: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ForSaleHE .service-card,
.ForSaleHE .feature-item,
.ForSaleHE .testimonial-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
.ForSaleHE .cta-button:focus,
.ForSaleHE .form-group input:focus,
.ForSaleHE .form-group textarea:focus {
    outline: 2px solid #e74c3c;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .ForSaleHE .hero-section,
    .ForSaleHE .final-cta-section {
        background: white !important;
        color: black !important;
    }

    .ForSaleHE .cta-button {
        display: none;
    }
}
