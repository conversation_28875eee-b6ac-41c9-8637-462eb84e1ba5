/* ForSaleHE Component Styles - Hebrew RTL */
.ForSaleHE {
    width: 100%;
    min-height: 100vh;
    direction: rtl;
    font-family: 'Poppins', 'Arial', sans-serif;
}

/* Container */
.ForSaleHE .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.ForSaleHE .hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    min-height: 80vh;
    direction: rtl;
}

.ForSaleHE .hero-content {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.ForSaleHE .hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.ForSaleHE .hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.9;
}

.ForSaleHE .hero-image {
    flex: 1;
    max-width: 600px;
    margin: 2rem auto 0;
}

.ForSaleHE .hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Buttons */
.ForSaleHE .cta-button {
    padding: 18px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 200px;
    position: relative;
    overflow: hidden;
    margin: 10px 0;
}

.ForSaleHE .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.ForSaleHE .cta-button:hover::before {
    left: 100%;
}

.ForSaleHE .cta-button.primary {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.4);
    border: 2px solid transparent;
}

.ForSaleHE .cta-button.primary:hover {
    box-shadow: 0 15px 40px rgba(231, 76, 60, 0.5);
    transform: translateY(-3px) scale(1.02);
}

.ForSaleHE .cta-button.secondary {
    background: transparent;
    color: #2c3e50;
    border: 2px solid #2c3e50;
    backdrop-filter: blur(10px);
}

.ForSaleHE .cta-button.secondary:hover {
    background: #2c3e50;
    color: white;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(44, 62, 80, 0.3);
}

.ForSaleHE .cta-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.ForSaleHE .cta-button:active {
    transform: translateY(-1px) scale(0.98);
}

/* Services Section */
.ForSaleHE .services-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForSaleHE .services-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForSaleHE .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForSaleHE .service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ForSaleHE .service-card:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.ForSaleHE .service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.ForSaleHE .service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForSaleHE .service-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Why Choose Section */
.ForSaleHE .why-choose-section {
    padding: 100px 0;
    background: white;
}

.ForSaleHE .why-choose-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForSaleHE .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForSaleHE .feature-item {
    padding: 2rem;
    border-right: 4px solid #e74c3c;
    border-left: none;
    background: #f8f9fa;
    border-radius: 10px;
}

.ForSaleHE .feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForSaleHE .feature-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* Testimonials Section */
.ForSaleHE .testimonials-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.ForSaleHE .testimonials-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.ForSaleHE .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ForSaleHE .testimonial-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ForSaleHE .testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.ForSaleHE .testimonial-author strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.ForSaleHE .testimonial-author span {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Contact Section */
.ForSaleHE .contact-section {
    padding: 100px 0;
    background: white;
}

.ForSaleHE .contact-section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.ForSaleHE .section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.ForSaleHE .contact-form {
    max-width: 700px;
    margin: 0 auto;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.ForSaleHE .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.ForSaleHE .form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.ForSaleHE .form-group label {
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
    font-size: 1rem;
}

.ForSaleHE .form-group input,
.ForSaleHE .form-group textarea {
    padding: 16px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: right;
    direction: rtl;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.ForSaleHE .form-group input:focus,
.ForSaleHE .form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    transform: translateY(-2px);
}

.ForSaleHE .form-group textarea {
    resize: vertical;
    min-height: 140px;
    font-family: inherit;
}

.ForSaleHE .form-group input::placeholder,
.ForSaleHE .form-group textarea::placeholder {
    color: #9ca3af;
    opacity: 1;
}

.ForSaleHE .submit-message {
    margin-top: 1.5rem;
    padding: 16px 24px;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    font-size: 1rem;
    animation: slideInUp 0.3s ease-out;
}

.ForSaleHE .submit-message.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.ForSaleHE .submit-message.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Button Container */
.ForSaleHE .form-button-container {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
}

/* Final CTA Section */
.ForSaleHE .final-cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    text-align: center;
}

.ForSaleHE .final-cta-section h2 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

.ForSaleHE .final-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ForSaleHE .container {
        padding: 0 15px;
    }

    .ForSaleHE .hero-section {
        padding: 60px 0;
        min-height: 70vh;
        flex-direction: column;
        text-align: center;
    }

    .ForSaleHE .hero-content h1 {
        font-size: 2.5rem;
    }

    .ForSaleHE .hero-subtitle {
        font-size: 1.1rem;
    }

    .ForSaleHE .hero-image {
        margin-top: 2rem;
        max-width: 100%;
    }

    .ForSaleHE .services-section,
    .ForSaleHE .why-choose-section,
    .ForSaleHE .testimonials-section,
    .ForSaleHE .contact-section,
    .ForSaleHE .final-cta-section {
        padding: 60px 0;
    }

    .ForSaleHE .services-section h2,
    .ForSaleHE .why-choose-section h2,
    .ForSaleHE .testimonials-section h2,
    .ForSaleHE .contact-section h2,
    .ForSaleHE .final-cta-section h2 {
        font-size: 2.2rem;
    }

    .ForSaleHE .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForSaleHE .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForSaleHE .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .ForSaleHE .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .ForSaleHE .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .ForSaleHE .hero-content h1 {
        font-size: 2rem;
    }

    .ForSaleHE .hero-subtitle {
        font-size: 1rem;
    }

    .ForSaleHE .services-section h2,
    .ForSaleHE .why-choose-section h2,
    .ForSaleHE .testimonials-section h2,
    .ForSaleHE .contact-section h2,
    .ForSaleHE .final-cta-section h2 {
        font-size: 1.8rem;
    }

    .ForSaleHE .service-card,
    .ForSaleHE .testimonial-card {
        padding: 1.5rem;
    }

    .ForSaleHE .feature-item {
        padding: 1.5rem;
    }
}

/* Animation Enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ForSaleHE .service-card,
.ForSaleHE .feature-item,
.ForSaleHE .testimonial-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
.ForSaleHE .cta-button:focus,
.ForSaleHE .form-group input:focus,
.ForSaleHE .form-group textarea:focus {
    outline: 2px solid #e74c3c;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .ForSaleHE .hero-section,
    .ForSaleHE .final-cta-section {
        background: white !important;
        color: black !important;
    }

    .ForSaleHE .cta-button {
        display: none;
    }
}
