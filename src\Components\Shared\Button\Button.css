/* Shared <PERSON><PERSON> Component Styles */
.btn {
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin: 10px 0;
    font-family: 'Poppins', sans-serif;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* <PERSON><PERSON> Variants */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover:not(:disabled) {
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.btn-secondary:hover:not(:disabled) {
    box-shadow: 0 12px 35px rgba(240, 147, 251, 0.6);
    transform: translateY(-2px);
}

/* Button Sizes */
.btn-small {
    padding: 12px 24px;
    font-size: 0.9rem;
    min-width: 120px;
}

.btn-medium {
    padding: 18px 40px;
    font-size: 1.1rem;
    min-width: 200px;
}

.btn-large {
    padding: 22px 50px;
    font-size: 1.2rem;
    min-width: 250px;
}

/* Shimmer Effect */
.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before:not(:disabled) {
    left: 100%;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .btn-medium {
        padding: 16px 32px;
        font-size: 1rem;
        min-width: 180px;
    }
    
    .btn-large {
        padding: 18px 40px;
        font-size: 1.1rem;
        min-width: 200px;
    }
}

@media (max-width: 480px) {
    .btn-small {
        padding: 10px 20px;
        font-size: 0.85rem;
        min-width: 100px;
    }
    
    .btn-medium {
        padding: 14px 28px;
        font-size: 0.95rem;
        min-width: 160px;
    }
    
    .btn-large {
        padding: 16px 32px;
        font-size: 1rem;
        min-width: 180px;
    }
}
