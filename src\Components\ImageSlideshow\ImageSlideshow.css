/* Image Slideshow Component */
.image-slideshow {
    position: relative;
    width: 100%;
    height: auto;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.slideshow-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.slideshow-image {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    border-radius: 15px;
}

/* Loading State */
.image-slideshow.loading {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 200px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation Arrows */
.slideshow-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.slideshow-arrow:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: translateY(-50%) scale(1.1);
}

.slideshow-arrow.prev {
    left: 15px;
}

.slideshow-arrow.next {
    right: 15px;
}

/* Dots Indicator */
.slideshow-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.dot.active {
    background: white;
    border-color: white;
    transform: scale(1.2);
}

.dot:hover {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
}

/* Image Counter */
.image-counter {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    z-index: 10;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slideshow-arrow {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .slideshow-arrow.prev {
        left: 10px;
    }
    
    .slideshow-arrow.next {
        right: 10px;
    }
    
    .slideshow-dots {
        bottom: 15px;
        gap: 8px;
    }
    
    .dot {
        width: 10px;
        height: 10px;
    }
    
    .image-counter {
        top: 10px;
        right: 10px;
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .slideshow-arrow {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .slideshow-dots {
        bottom: 10px;
        gap: 6px;
    }
    
    .dot {
        width: 8px;
        height: 8px;
    }
}

/* Animation Enhancements */
.slideshow-container:hover .slideshow-arrow {
    opacity: 1;
}

.slideshow-arrow {
    opacity: 0.7;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Accessibility */
.slideshow-arrow:focus,
.dot:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .slideshow-arrow,
    .slideshow-dots,
    .image-counter {
        display: none;
    }
}
