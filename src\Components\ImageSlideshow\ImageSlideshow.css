/* Image Slideshow Component */
.image-slideshow {
    position: relative;
    width: 100%;
    height: 400px; /* Fixed height */
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.slideshow-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden; /* Ensure images don't overflow on mobile */
}

.slideshow-image {
    width: 100%;
    height: 100%; /* Fill container height */
    display: block;
    border-radius: 15px;
    object-fit: cover;
    object-position: center center;
    /* Mobile-specific optimizations */
    -webkit-object-fit: cover;
    -webkit-object-position: center center;
    /* Fallback for older browsers that don't support object-fit */
    max-width: 100%;
    max-height: 100%;
}

/* Loading State */
.image-slideshow.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation Arrows */
.slideshow-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.slideshow-arrow:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: translateY(-50%) scale(1.1);
}

.slideshow-arrow.prev {
    left: 15px;
}

.slideshow-arrow.next {
    right: 15px;
}

/* Dots Indicator */
.slideshow-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.dot.active {
    background: white;
    border-color: white;
    transform: scale(1.2);
}

.dot:hover {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
}



/* Responsive Design */
@media (max-width: 768px) and (min-width: 481px) {
    .image-slideshow {
        height: 350px; /* Medium height on tablets */
        border-radius: 10px; /* Slightly smaller border radius */
    }

    .slideshow-image {
        border-radius: 10px;
    }

    .slideshow-arrow {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .slideshow-arrow.prev {
        left: 10px;
    }

    .slideshow-arrow.next {
        right: 10px;
    }

    .slideshow-dots {
        bottom: 15px;
        gap: 8px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }


}

@media (max-width: 480px) {
    .image-slideshow {
        height: 300px; /* Constant height on mobile */
        width: 100vw; /* Full viewport width */
        margin-left: calc(-50vw + 50%); /* Center and extend to full width */
        margin-bottom: 0; /* Remove bottom spacing on mobile */
        border-radius: 0; /* Remove border radius on mobile */
        box-shadow: none; /* Remove shadow on mobile for cleaner look */
    }

    .slideshow-image {
        /* Ensure images work properly on mobile devices */
        object-fit: cover !important;
        object-position: center center !important;
        -webkit-object-fit: cover !important;
        -webkit-object-position: center center !important;
        border-radius: 0; /* Remove border radius on mobile */
        /* Hardware acceleration for smooth animations */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        will-change: transform;
    }

    .slideshow-arrow {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .slideshow-dots {
        bottom: 10px;
        gap: 6px;
    }

    .dot {
        width: 8px;
        height: 8px;
    }
}

/* Animation Enhancements */
.slideshow-container:hover .slideshow-arrow {
    opacity: 1;
}

.slideshow-arrow {
    opacity: 0.7;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Accessibility */
.slideshow-arrow:focus,
.dot:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .slideshow-arrow,
    .slideshow-dots {
        display: none;
    }
}
